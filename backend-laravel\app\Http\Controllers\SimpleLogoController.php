<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class SimpleLogoController extends Controller
{
    /**
     * Upload logo file
     */
    public function upload(Request $request)
    {
        try {
            // Check if file exists in request
            if (!$request->hasFile('logo')) {
                return response()->json([
                    'success' => false,
                    'error' => 'No file uploaded',
                    'message' => 'Please select a logo file to upload'
                ], 400);
            }

            $logoFile = $request->file('logo');

            // Check if file is valid
            if (!$logoFile->isValid()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file',
                    'message' => 'The uploaded file is not valid: ' . $logoFile->getErrorMessage()
                ], 400);
            }

            // Manual validation
            $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            $maxSize = 5 * 1024 * 1024; // 5MB

            if (!in_array($logoFile->getMimeType(), $allowedMimes)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file type',
                    'message' => 'Only JPEG, PNG, and WebP images are allowed'
                ], 400);
            }

            if ($logoFile->getSize() > $maxSize) {
                return response()->json([
                    'success' => false,
                    'error' => 'File too large',
                    'message' => 'File size must be less than 5MB'
                ], 400);
            }

            // Generate unique filename: logo_timestamp.ext
            $timestamp = time();
            $extension = $logoFile->getClientOriginalExtension();
            $filename = "logo_{$timestamp}.{$extension}";

            // Store file to frontend/public/images/logo directory
            $uploadPath = base_path('../frontend/public/images/logo');

            // Create directory if it doesn't exist
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move file to frontend public directory
            $moved = $logoFile->move($uploadPath, $filename);
            $path = $moved ? "images/logo/{$filename}" : null;

            if (!$path) {
                return response()->json([
                    'success' => false,
                    'error' => 'Storage failed',
                    'message' => 'Could not store uploaded file'
                ], 500);
            }

            // Get file info
            $fullPath = base_path('../frontend/public/' . $path);
            $fileSize = $logoFile->getSize();
            $formattedSize = $this->formatBytes($fileSize);

            // Generate URL - akan diakses dari frontend public
            $logoUrl = '/' . $path;

            // Save filename to settings
            $this->saveLogoToSettings($filename, $logoUrl);

            return response()->json([
                'success' => true,
                'message' => 'Logo uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'original_name' => $logoFile->getClientOriginalName(),
                    'file_path' => $filePath,
                    'file_size' => $fileSize,
                    'formatted_size' => $formattedSize,
                    'mime_type' => $logoFile->getMimeType(),
                    'url' => $logoUrl,
                    'uploaded_at' => date('Y-m-d H:i:s')
                ]
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Logo upload error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Upload failed',
                'message' => 'An error occurred during upload: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get current active logo
     */
    public function getCurrent()
    {
        try {
            // Get logo from settings
            $logoUrl = $this->getLogoFromSettings();

            if (!$logoUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'No logo found'
                ], 404);
            }

            // Extract filename from URL
            $filename = basename(parse_url($logoUrl, PHP_URL_PATH));

            // Get file info if exists
            $logoPath = base_path('../frontend/public/images/logo/' . $filename);
            $fileExists = file_exists($logoPath);
            $fileSize = $fileExists ? filesize($logoPath) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'url' => $logoUrl,
                    'filename' => $filename,
                    'file_exists' => $fileExists,
                    'file_size' => $fileSize,
                    'formatted_size' => $this->formatBytes($fileSize)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get current logo',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Save logo info to settings
     */
    private function saveLogoToSettings($filename, $url)
    {
        // Update logoUrl in settings
        \App\Models\Setting::updateOrCreate(
            ['key' => 'logoUrl'],
            [
                'value' => $url,
                'category' => 'general',
                'is_public' => true,
                'updated_by' => auth()->id() ?? 1
            ]
        );
    }
    
    /**
     * Get logo from settings
     */
    private function getLogoFromSettings()
    {
        $setting = \App\Models\Setting::where('key', 'logoUrl')->first();
        return $setting ? $setting->value : null;
    }
    
    /**
     * Upload favicon file
     */
    public function uploadFavicon(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'favicon' => 'required|image|mimes:ico,png,jpg,jpeg|max:1024', // 1MB max for favicon
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            $faviconFile = $request->file('favicon');

            // Generate unique filename: favicon_timestamp.ext
            $timestamp = time();
            $extension = $faviconFile->getClientOriginalExtension();
            $filename = "favicon_{$timestamp}.{$extension}";

            // Define favicon path in public/uploads/images/favicon
            $faviconPath = public_path('uploads/images/favicon');

            // Create directory if it doesn't exist
            if (!is_dir($faviconPath)) {
                mkdir($faviconPath, 0755, true);
            }

            // Move file to uploads folder
            $faviconFile->move($faviconPath, $filename);

            // Get file size for display
            $filePath = $faviconPath . '/' . $filename;
            $fileSize = filesize($filePath);
            $formattedSize = $this->formatBytes($fileSize);

            // Generate URL - akan diakses melalui backend
            $faviconUrl = url("uploads/images/favicon/{$filename}");

            // Save filename to settings
            $this->saveFaviconToSettings($filename, $faviconUrl);

            return response()->json([
                'success' => true,
                'message' => 'Favicon uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'original_name' => $faviconFile->getClientOriginalName(),
                    'file_path' => $filePath,
                    'file_size' => $fileSize,
                    'formatted_size' => $formattedSize,
                    'mime_type' => $faviconFile->getMimeType(),
                    'url' => $faviconUrl,
                    'uploaded_at' => date('Y-m-d H:i:s')
                ]
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Upload failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current active favicon
     */
    public function getCurrentFavicon()
    {
        try {
            // Get favicon from settings
            $faviconUrl = $this->getFaviconFromSettings();

            if (!$faviconUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'No favicon found'
                ], 404);
            }

            // Extract filename from URL
            $filename = basename(parse_url($faviconUrl, PHP_URL_PATH));

            // Get file info if exists
            $faviconPath = public_path('uploads/images/favicon/' . $filename);
            $fileExists = file_exists($faviconPath);
            $fileSize = $fileExists ? filesize($faviconPath) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'url' => $faviconUrl,
                    'filename' => $filename,
                    'file_exists' => $fileExists,
                    'file_size' => $fileSize,
                    'formatted_size' => $this->formatBytes($fileSize)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get current favicon',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save favicon info to settings
     */
    private function saveFaviconToSettings($filename, $url)
    {
        // Update faviconUrl in settings
        \App\Models\Setting::updateOrCreate(
            ['key' => 'faviconUrl'],
            [
                'value' => $url,
                'category' => 'appearance',
                'is_public' => true,
                'updated_by' => auth()->id() ?? 1
            ]
        );
    }

    /**
     * Get favicon from settings
     */
    private function getFaviconFromSettings()
    {
        $setting = \App\Models\Setting::where('key', 'faviconUrl')->first();
        return $setting ? $setting->value : null;
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
