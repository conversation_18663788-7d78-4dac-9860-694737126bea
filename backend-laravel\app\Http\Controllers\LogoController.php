<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class LogoController extends Controller
{
    /**
     * Upload logo
     */
    public function upload(Request $request)
    {
        try {
            // Debug incoming request
            \Log::info('Logo upload request received', [
                'has_file' => $request->hasFile('logo'),
                'all_files' => $request->allFiles(),
                'all_input' => $request->all()
            ]);

            // Check if file exists
            if (!$request->hasFile('logo')) {
                \Log::error('No logo file in request');
                return response()->json([
                    'success' => false,
                    'message' => 'No file uploaded. Field name should be "logo"'
                ], 400);
            }

            // Validate file
            $validator = \Validator::make($request->all(), [
                'logo' => 'required|image|mimes:jpeg,jpg,png,svg|max:2048'
            ]);

            if ($validator->fails()) {
                \Log::error('Validation failed', $validator->errors()->toArray());
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $file = $request->file('logo');
            
            // Generate unique filename
            $timestamp = time();
            $extension = $file->getClientOriginalExtension();
            $filename = "school_logo_{$timestamp}.{$extension}";

            // Define paths - save to backend-laravel/public/images/logo
            $uploadPath = public_path('images/logo');

            // Create directory if not exists
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move file to public directory
            $file->move($uploadPath, $filename);

            // Deactivate all existing logos
            DB::table('school_logos')->update(['is_active' => false]);

            // Save to database
            $logoId = DB::table('school_logos')->insertGetId([
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'file_path' => "/images/logo/{$filename}",
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'is_active' => true,
                'description' => $request->input('description', 'Logo sekolah'),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Get saved logo
            $logo = DB::table('school_logos')->where('id', $logoId)->first();

            // Generate URL - point to backend Laravel public folder
            $logoUrl = "http://localhost:8000/images/logo/{$filename}";

            return response()->json([
                'success' => true,
                'message' => 'Logo berhasil diupload',
                'data' => [
                    'id' => $logoId,
                    'filename' => $filename,
                    'original_name' => $file->getClientOriginalName(),
                    'url' => $logoUrl,
                    'file_path' => "/images/logo/{$filename}",
                    'mime_type' => $file->getMimeType(),
                    'file_size' => $file->getSize(),
                    'formatted_size' => $this->formatFileSize($file->getSize()),
                    'is_active' => true,
                    'description' => 'Logo sekolah',
                    'uploaded_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current active logo
     */
    public function getCurrent()
    {
        try {
            $logo = \App\Models\SchoolLogo::where('is_active', true)
                                          ->orderBy('created_at', 'desc')
                                          ->first();

            if (!$logo) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active logo found'
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'original_name' => $logo->original_name,
                    'url' => $logo->url, // Uses model accessor
                    'file_path' => $logo->file_path,
                    'mime_type' => $logo->mime_type,
                    'file_size' => $logo->file_size,
                    'formatted_size' => $logo->formatted_size, // Uses model accessor
                    'is_active' => $logo->is_active,
                    'description' => $logo->description,
                    'uploaded_at' => $logo->created_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all logos
     */
    public function getAll()
    {
        try {
            $logos = DB::table('school_logos')
                      ->orderBy('created_at', 'desc')
                      ->get();

            $logoData = $logos->map(function ($logo) {
                return [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'original_name' => $logo->original_name,
                    'url' => "http://localhost:8000/uploads/images/logo/{$logo->filename}",
                    'file_path' => $logo->file_path,
                    'mime_type' => $logo->mime_type,
                    'file_size' => $logo->file_size,
                    'formatted_size' => $this->formatFileSize($logo->file_size),
                    'is_active' => $logo->is_active,
                    'description' => $logo->description,
                    'uploaded_at' => $logo->created_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $logoData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format file size
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
