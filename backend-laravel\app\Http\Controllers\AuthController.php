<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    /**
     * Login user with JWT
     */
    public function login(Request $request)
    {
        try {
            \Log::info('Login attempt', [
                'email' => $request->email,
                'has_password' => !empty($request->password)
            ]);

            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
                'password' => 'required|string|min:6',
            ]);

            if ($validator->fails()) {
                \Log::warning('Login validation failed', $validator->errors()->toArray());
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

            $credentials = $request->only('email', 'password');

            // Check if user exists first
            $user = User::where('email', $credentials['email'])->first();
            if (!$user) {
                \Log::warning('Login failed - user not found', ['email' => $credentials['email']]);
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid credentials'
                ], 401);
            }

            // Attempt to verify the credentials and create a token for the user
            if (!$token = JWTAuth::attempt($credentials)) {
                \Log::warning('Login failed - invalid credentials', ['email' => $credentials['email']]);
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid credentials'
                ], 401);
            }

            // Get the authenticated user
            $user = Auth::user();

            // Check if user is active
            if (!$user->is_active) {
                // Invalidate the token if user is inactive
                JWTAuth::invalidate($token);

                \Log::warning('Login failed - user inactive', ['user_id' => $user->id]);
                return response()->json([
                    'success' => false,
                    'error' => 'Account is deactivated'
                ], 403);
            }

            // Update last login
            $user->update(['last_login' => now()]);

            \Log::info('Login successful', ['user_id' => $user->id, 'email' => $user->email]);

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => config('jwt.ttl') * 60, // TTL in seconds (1440 * 60 = 86400 seconds = 24 hours)
                'expires_in_minutes' => config('jwt.ttl'), // TTL in minutes (1440 minutes = 24 hours)
                'expires_at' => now()->addMinutes(config('jwt.ttl'))->toISOString(),
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'avatar' => $user->avatar,
                    'last_login' => $user->last_login,
                ]
            ]);

        } catch (JWTException $e) {
            \Log::error('JWT Exception during login', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'error' => 'Could not create token: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            \Log::error('General exception during login', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'error' => 'Login failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout user - invalidate JWT token
     */
    public function logout(Request $request)
    {
        try {
            \Log::info('JWT Logout request received', [
                'user_id' => $request->user() ? $request->user()->id : null,
                'has_bearer_token' => $request->bearerToken() ? true : false,
                'request_method' => $request->method(),
                'request_path' => $request->path()
            ]);

            // Invalidate the JWT token
            JWTAuth::invalidate(JWTAuth::getToken());

            \Log::info('JWT Logout successful');

            return response()->json([
                'success' => true,
                'message' => 'Successfully logged out'
            ]);
        } catch (JWTException $e) {
            \Log::error('JWT Logout error', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to logout, please try again'
            ], 500);
        }
    }

    /**
     * Get authenticated user via JWT
     */
    public function me(Request $request)
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'error' => 'User not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'avatar' => $user->avatar,
                    'last_login' => $user->last_login,
                    'is_active' => $user->is_active,
                ]
            ]);
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'error' => 'Token is invalid'
            ], 401);
        }
    }

    /**
     * Register new user (admin only)
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'role' => 'required|in:admin,user',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()->first()
            ], 400);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'User created successfully',
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
            ]
        ], 201);
    }
}
