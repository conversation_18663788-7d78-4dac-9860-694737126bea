<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class GalleryController extends Controller
{
    /**
     * Get all gallery items (public)
     */
    public function index(Request $request)
    {
        $query = Gallery::with('uploader:id,name')
            ->active()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');

        // Category filter
        if ($request->has('category') && $request->category !== 'all') {
            $query->category($request->category);
        }

        // Featured filter
        if ($request->has('featured') && $request->boolean('featured')) {
            $query->featured();
        }

        // Carousel filter
        if ($request->has('carousel') && $request->boolean('carousel')) {
            $query->carouselPinned();
        }

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        $galleries = $query->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $galleries->items(),
            'pagination' => [
                'current_page' => $galleries->currentPage(),
                'last_page' => $galleries->lastPage(),
                'per_page' => $galleries->perPage(),
                'total' => $galleries->total(),
            ]
        ]);
    }

    /**
     * Get single gallery item by ID (public)
     */
    public function show($id)
    {
        $gallery = Gallery::with('uploader:id,name')
            ->active()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $gallery
        ]);
    }

    /**
     * Get all gallery items for admin (including inactive) - Optimized
     */
    public function adminIndex(Request $request)
    {
        // Start with optimized query
        $query = Gallery::select([
                'id', 'title', 'description', 'image_url', 'category',
                'uploaded_by', 'is_active', 'featured', 'carousel_pinned',
                'sort_order', 'created_at', 'updated_at'
            ])
            ->with(['uploader:id,name'])
            ->orderBy('created_at', 'desc');

        // Search optimization - use index
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Category filter - use index
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Status filter - use index
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Featured filter - use index
        if ($request->has('featured')) {
            if ($request->featured === 'true') {
                $query->where('featured', true);
            } elseif ($request->featured === 'false') {
                $query->where('featured', false);
            }
        }

        // Carousel filter - use index
        if ($request->has('carousel')) {
            if ($request->carousel === 'true') {
                $query->where('carousel_pinned', true);
            } elseif ($request->carousel === 'false') {
                $query->where('carousel_pinned', false);
            }
        }

        // Set pagination to 5 items per page for testing
        $perPage = min($request->get('per_page', 5), 50); // Default 5 items per page
        $galleries = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $galleries->items(),
            'pagination' => [
                'current_page' => $galleries->currentPage(),
                'last_page' => $galleries->lastPage(),
                'per_page' => $galleries->perPage(),
                'total' => $galleries->total(),
                'from' => $galleries->firstItem(),
                'to' => $galleries->lastItem(),
            ]
        ]);
    }

    /**
     * Store new gallery item (admin only)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'category' => 'required|in:Kegiatan Rutin,Kegiatan Khusus,Prestasi,Fasilitas,Ekstrakurikuler',
            'featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            // Handle file upload menggunakan Laravel Storage
            if (!$request->hasFile('image')) {
                return response()->json([
                    'success' => false,
                    'error' => 'No image file',
                    'message' => 'Silakan pilih file gambar'
                ], 400);
            }

            $image = $request->file('image');

            // Validasi file
            if (!$image->isValid()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file',
                    'message' => 'File yang diupload tidak valid'
                ], 400);
            }

            // Generate filename unik
            $timestamp = time();
            $extension = $this->getProperExtension($image);
            $filename = "gallery_{$timestamp}.{$extension}";

            // Save file to backend Laravel public/images/gallery directory
            $uploadPath = public_path('images/gallery');

            // Create directory if it doesn't exist
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if (!$image->move($uploadPath, $filename)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Storage failed',
                    'message' => 'Gagal menyimpan file gambar'
                ], 500);
            }

            // Generate URL untuk akses file dari backend
            $imageUrl = "http://localhost:8000/images/gallery/{$filename}";

            // Create gallery record
            $gallery = Gallery::create([
                'title' => $request->get('title'),
                'description' => $request->get('description'),
                'category' => $request->get('category'),
                'image_url' => $imageUrl,
                'uploaded_by' => auth()->id() ?? 1,
                'is_active' => $request->boolean('is_active', true),
                'featured' => $request->boolean('featured', false),
                'carousel_pinned' => $request->boolean('carousel_pinned', false),
                'sort_order' => $request->get('sort_order', 0),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Gallery item created successfully',
                'data' => $gallery->load('uploader:id,name')
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Gallery upload error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Upload failed',
                'message' => 'Terjadi kesalahan saat upload: ' . $e->getMessage()
            ], 500);
        }

        // If no image uploaded, create gallery without image
        $gallery = Gallery::create([
            'title' => $request->title,
            'description' => $request->description,
            'image_url' => null,
            'category' => $request->category,
            'uploaded_by' => auth()->id() ?? 1, // Default to user ID 1 if no auth
            'featured' => $request->boolean('featured', false),
            'carousel_pinned' => $request->boolean('carousel_pinned', false),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->get('sort_order', 0),
        ]);

        $gallery->load('uploader:id,name');

        return response()->json([
            'success' => true,
            'message' => 'Gallery item created successfully (no image)',
            'data' => $gallery
        ], 201);
    }

    /**
     * Update gallery item (admin only)
     */
    public function update(Request $request, $id)
    {
        try {
            $gallery = Gallery::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'category' => 'required|in:Kegiatan Rutin,Kegiatan Khusus,Prestasi,Fasilitas,Ekstrakurikuler',
            'featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $updateData = [
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'featured' => $request->boolean('featured', false),
            'carousel_pinned' => $request->boolean('carousel_pinned', false),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->get('sort_order', 0),
        ];

        // IMPORTANT: Preserve existing image_url if no image changes are requested
        $preserveImage = true;

        // Explicitly preserve image_url unless we're changing it
        if ($gallery->image_url && $preserveImage) {
            $updateData['image_url'] = $gallery->image_url;
        }

        // Handle image deletion if requested
        $deleteImage = $request->boolean('delete_image', false);
        if ($deleteImage) {
            // Delete old image from storage if exists
            if ($gallery->image_url) {
                $this->deleteOldImage($gallery->image_url);
            }
            $updateData['image_url'] = null;
            $preserveImage = false;
        }
        // Handle file upload if new image provided
        else if ($request->hasFile('image')) {
            $preserveImage = false;

            // Delete old image first if exists
            if ($gallery->image_url) {
                $this->deleteOldImage($gallery->image_url);
            }

            $image = $request->file('image');

            // Validasi file
            if (!$image->isValid()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file',
                    'message' => 'File yang diupload tidak valid'
                ], 400);
            }

            // Generate filename unik
            $timestamp = time();
            $extension = $this->getProperExtension($image);
            $filename = "gallery_{$gallery->id}_{$timestamp}.{$extension}";

            // Save file to backend Laravel public/images/gallery directory
            $uploadPath = public_path('images/gallery');

            // Create directory if it doesn't exist
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if (!$image->move($uploadPath, $filename)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to save image file'
                ], 500);
            }

            // Generate URL untuk akses file dari backend
            $imageUrl = "http://localhost:8000/images/gallery/{$filename}";
            $updateData['image_url'] = $imageUrl;
        }

            $gallery->update($updateData);
            $gallery->load('uploader:id,name');

            return response()->json([
                'success' => true,
                'message' => 'Gallery item updated successfully',
                'data' => $gallery
            ]);

        } catch (\Exception $e) {
            \Log::error('Gallery update error', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Update failed',
                'message' => 'Terjadi kesalahan saat update: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete gallery item (admin only)
     */
    public function destroy($id)
    {
        try {
            \Log::info('Gallery delete request', ['id' => $id]);

            $gallery = Gallery::findOrFail($id);
            \Log::info('Gallery found', ['gallery' => $gallery->toArray()]);

            // Delete image file if exists using helper method
            if ($gallery->image_url) {
                \Log::info('Deleting image file', ['image_url' => $gallery->image_url]);
                $this->deleteOldImage($gallery->image_url);
            }

            $gallery->delete();
            \Log::info('Gallery deleted successfully', ['id' => $id]);

            return response()->json([
                'success' => true,
                'message' => 'Gallery item deleted successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Gallery delete error', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Delete failed',
                'message' => 'Gagal menghapus gallery: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to delete old image file
     */
    private function deleteOldImage($imageUrl)
    {
        if (!$imageUrl) return;

        try {
            // Handle backend Laravel public/images/gallery path (NEW)
            if (strpos($imageUrl, 'localhost:8000/images/gallery/') !== false) {
                $filename = basename($imageUrl);
                $filePath = public_path('images/gallery/' . $filename);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle /images/gallery path (relative format)
            elseif (strpos($imageUrl, '/images/gallery/') !== false) {
                $filename = basename($imageUrl);
                $filePath = public_path('images/gallery/' . $filename);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle uploads/images/gallery path (old backend format)
            else if (strpos($imageUrl, '/uploads/images/gallery/') !== false) {
                $filename = basename($imageUrl);
                $filePath = base_path('../uploads/images/gallery/' . $filename);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle uploads/images path (news format - for backward compatibility)
            else if (strpos($imageUrl, '/uploads/images/') !== false) {
                $filename = basename($imageUrl);
                $filePath = base_path('../uploads/images/' . $filename);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle storage path (old format)
            else if (strpos($imageUrl, '/storage/') !== false) {
                $oldPath = str_replace(asset('storage/'), '', $imageUrl);
                if (Storage::disk('public')->exists($oldPath)) {
                    Storage::disk('public')->delete($oldPath);
                }
            }
        } catch (\Exception $e) {
            \Log::error('Failed to delete old gallery image', [
                'image_url' => $imageUrl,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Pin/Unpin gallery item to carousel (admin only)
     */
    public function toggleCarouselPin($id)
    {
        $gallery = Gallery::findOrFail($id);

        $gallery->carousel_pinned = !$gallery->carousel_pinned;
        $gallery->save();

        return response()->json([
            'success' => true,
            'message' => $gallery->carousel_pinned ? 'Gambar berhasil di-pin ke carousel' : 'Gambar berhasil di-unpin dari carousel',
            'data' => $gallery
        ]);
    }

    /**
     * Get carousel images (public)
     */
    public function getCarouselImages()
    {
        $images = Gallery::with('uploader:id,name')
            ->active()
            ->carouselPinned()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $images
        ]);
    }

    /**
     * Get proper file extension from uploaded file
     */
    private function getProperExtension($file) {
        // Dapatkan ekstensi asli dari file
        $originalExtension = $file->getClientOriginalExtension();

        // Jika ekstensi kosong atau tidak valid, coba dari MIME type
        if (empty($originalExtension)) {
            $mimeType = $file->getMimeType();
            $extensionFromMime = $this->getExtensionFromMimeType($mimeType);
            if ($extensionFromMime) {
                return $extensionFromMime;
            }
        }

        // Pastikan ekstensi dalam lowercase dan valid
        $extension = strtolower($originalExtension);

        // Validasi ekstensi yang diizinkan
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (in_array($extension, $allowedExtensions)) {
            return $extension;
        }

        // Default fallback
        return 'jpg';
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType($mimeType) {
        $mimeToExtension = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];

        return $mimeToExtension[$mimeType] ?? null;
    }
}
