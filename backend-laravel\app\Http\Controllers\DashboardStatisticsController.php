<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\VisitorStatistic;
use App\Models\News;
use App\Models\Gallery;
use Carbon\Carbon;

class DashboardStatisticsController extends Controller
{
    /**
     * Get dashboard statistics for charts
     */
    public function getChartData(Request $request)
    {
        try {
            $days = $request->get('days', 7); // Default 7 days

            // Get date range
            $endDate = Carbon::now();
            $startDate = Carbon::now()->subDays($days - 1);

            // Generate all dates in range
            $dateRange = [];
            for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
                $dateRange[] = $date->format('Y-m-d');
            }

            // Get visitor statistics
            $visitorStats = VisitorStatistic::whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            // Get news statistics (count by created_at date)
            $newsStats = News::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            // Get gallery statistics (count by created_at date)
            $galleryStats = Gallery::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->keyBy('date');

            // Prepare chart data
            $chartData = [
                'labels' => [],
                'datasets' => [
                    [
                        'label' => 'Pengunjung',
                        'data' => [],
                        'borderColor' => 'rgb(59, 130, 246)',
                        'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                        'tension' => 0.4
                    ],
                    [
                        'label' => 'Berita',
                        'data' => [],
                        'borderColor' => 'rgb(16, 185, 129)',
                        'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                        'tension' => 0.4
                    ],
                    [
                        'label' => 'Gallery',
                        'data' => [],
                        'borderColor' => 'rgb(139, 92, 246)',
                        'backgroundColor' => 'rgba(139, 92, 246, 0.1)',
                        'tension' => 0.4
                    ]
                ]
            ];

            // Fill data for each date
            foreach ($dateRange as $date) {
                $chartData['labels'][] = Carbon::parse($date)->format('d/m');

                // Visitor data
                $visitorCount = isset($visitorStats[$date]) ? $visitorStats[$date]->visitors_count : 0;
                $chartData['datasets'][0]['data'][] = $visitorCount;

                // News data
                $newsCount = isset($newsStats[$date]) ? $newsStats[$date]->count : 0;
                $chartData['datasets'][1]['data'][] = $newsCount;

                // Gallery data
                $galleryCount = isset($galleryStats[$date]) ? $galleryStats[$date]->count : 0;
                $chartData['datasets'][2]['data'][] = $galleryCount;
            }

            return response()->json([
                'success' => true,
                'data' => $chartData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data statistik',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get summary statistics
     */
    public function getSummaryStats()
    {
        try {
            $today = Carbon::today();
            $yesterday = Carbon::yesterday();
            $thisWeek = Carbon::now()->startOfWeek();
            $lastWeek = Carbon::now()->subWeek()->startOfWeek();

            // Today's stats
            $todayVisitors = VisitorStatistic::where('date', $today)->first();
            $todayVisitorCount = $todayVisitors ? $todayVisitors->visitors_count : 0;

            // Yesterday's stats for comparison
            $yesterdayVisitors = VisitorStatistic::where('date', $yesterday)->first();
            $yesterdayVisitorCount = $yesterdayVisitors ? $yesterdayVisitors->visitors_count : 0;

            // Calculate percentage change
            $visitorChange = $yesterdayVisitorCount > 0
                ? (($todayVisitorCount - $yesterdayVisitorCount) / $yesterdayVisitorCount) * 100
                : 0;

            // News stats
            $totalNews = News::count();
            $newsThisWeek = News::where('created_at', '>=', $thisWeek)->count();
            $newsLastWeek = News::whereBetween('created_at', [$lastWeek, $thisWeek])->count();
            $newsChange = $newsLastWeek > 0 ? (($newsThisWeek - $newsLastWeek) / $newsLastWeek) * 100 : 0;

            // Gallery stats
            $totalGallery = Gallery::count();
            $galleryThisWeek = Gallery::where('created_at', '>=', $thisWeek)->count();
            $galleryLastWeek = Gallery::whereBetween('created_at', [$lastWeek, $thisWeek])->count();
            $galleryChange = $galleryLastWeek > 0 ? (($galleryThisWeek - $galleryLastWeek) / $galleryLastWeek) * 100 : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'visitors' => [
                        'today' => $todayVisitorCount,
                        'total' => VisitorStatistic::sum('visitors_count'),
                        'change' => round($visitorChange, 1)
                    ],
                    'news' => [
                        'total' => $totalNews,
                        'this_week' => $newsThisWeek,
                        'change' => round($newsChange, 1)
                    ],
                    'gallery' => [
                        'total' => $totalGallery,
                        'this_week' => $galleryThisWeek,
                        'change' => round($galleryChange, 1)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil ringkasan statistik',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
