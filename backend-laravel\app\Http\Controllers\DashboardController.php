<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\Gallery;
use App\Models\Contact;
use App\Models\User;
use App\Models\Activity;
use Illuminate\Http\Request;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Get public dashboard statistics (no authentication required)
     */
    public function getPublicStats()
    {
        // Total Berita (hanya yang published)
        $totalNews = News::where('published', true)->count();

        // Total Galeri (hanya yang aktif)
        $totalGallery = Gallery::where('is_active', true)->count();

        // Total Pengunjung (simulasi - nanti bisa diganti dengan real analytics)
        $totalVisitors = 1250; // Hardcoded untuk demo

        // Pesan Kontak
        $totalContacts = Contact::count();

        return response()->json([
            'success' => true,
            'data' => [
                'totalNews' => [
                    'count' => $totalNews,
                    'growth' => "+12% dari bulan lalu",
                    'trend' => 'up'
                ],
                'totalGallery' => [
                    'count' => $totalGallery,
                    'growth' => "+8% dari bulan lalu",
                    'trend' => 'up'
                ],
                'totalVisitors' => [
                    'count' => $totalVisitors,
                    'growth' => "+25% dari bulan lalu",
                    'trend' => 'up'
                ],
                'totalContacts' => [
                    'count' => $totalContacts,
                    'growth' => "+5% dari bulan lalu",
                    'trend' => 'up'
                ]
            ]
        ]);
    }

    /**
     * Get dashboard statistics (requires authentication)
     */
    public function getStats()
    {
        // Total Berita
        $totalNews = News::count();

        // Total Galeri
        $totalGallery = Gallery::where('is_active', true)->count();

        // Total Pengunjung (akan diupdate nanti dengan real analytics)
        $totalVisitors = 0;

        // Pesan Kontak
        $totalContacts = Contact::count();

        return response()->json([
            'success' => true,
            'data' => [
                'totalNews' => [
                    'count' => $totalNews,
                    'growth' => "+0% dari bulan lalu",
                    'trend' => 'neutral'
                ],
                'totalGallery' => [
                    'count' => $totalGallery,
                    'growth' => "+0% dari bulan lalu",
                    'trend' => 'neutral'
                ],
                'totalVisitors' => [
                    'count' => $totalVisitors,
                    'growth' => "+0% dari bulan lalu",
                    'trend' => 'neutral'
                ],
                'totalContacts' => [
                    'count' => $totalContacts,
                    'growth' => "+0% dari bulan lalu",
                    'trend' => 'neutral'
                ]
            ]
        ]);
    }

    /**
     * Get recent news
     */
    public function getRecentNews()
    {
        $recentNews = News::orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($news) {
                return [
                    'id' => $news->id,
                    'title' => $news->title,
                    'status' => $news->published ? 'Published' : 'Draft',
                    'views' => 0, // Mulai dari 0
                    'created_at' => $news->created_at->format('Y-m-d'),
                    'author' => 'Admin'
                ];
            });

        // Jika tidak ada berita, return array kosong
        if ($recentNews->isEmpty()) {
            $recentNews = collect([]);
        }

        return response()->json([
            'success' => true,
            'data' => $recentNews
        ]);
    }

    /**
     * Get recent activities
     */
    public function getRecentActivities()
    {
        // Mulai dengan aktivitas kosong
        $activities = collect();

        // Berita terbaru (jika ada)
        $recentNews = News::orderBy('created_at', 'desc')->limit(2)->get();
        foreach ($recentNews as $news) {
            $activities->push([
                'type' => 'news',
                'icon' => 'document-text',
                'color' => 'green',
                'title' => 'Berita baru ditambahkan',
                'description' => $news->title,
                'time' => $news->created_at->diffForHumans(),
                'created_at' => $news->created_at
            ]);
        }

        // Galeri terbaru (jika ada)
        $recentGallery = Gallery::where('is_active', true)
            ->orderBy('created_at', 'desc')->limit(2)->get();
        foreach ($recentGallery as $gallery) {
            $activities->push([
                'type' => 'gallery',
                'icon' => 'photograph',
                'color' => 'blue',
                'title' => 'Galeri diperbarui',
                'description' => $gallery->title,
                'time' => $gallery->created_at->diffForHumans(),
                'created_at' => $gallery->created_at
            ]);
        }

        // Kontak terbaru (jika ada)
        $recentContacts = Contact::orderBy('created_at', 'desc')->limit(2)->get();
        foreach ($recentContacts as $contact) {
            $activities->push([
                'type' => 'contact',
                'icon' => 'mail',
                'color' => 'orange',
                'title' => 'Pesan kontak baru',
                'description' => "Dari {$contact->name} - {$contact->subject}",
                'time' => $contact->created_at->diffForHumans(),
                'created_at' => $contact->created_at
            ]);
        }

        // Jika tidak ada aktivitas, buat pesan default
        if ($activities->isEmpty()) {
            $activities->push([
                'type' => 'system',
                'icon' => 'information-circle',
                'color' => 'gray',
                'title' => 'Belum ada aktivitas',
                'description' => 'Sistem siap digunakan',
                'time' => 'Baru saja',
                'created_at' => now()
            ]);
        }

        // Sort by created_at desc and take 5
        $sortedActivities = $activities->sortByDesc('created_at')->take(5)->values();

        return response()->json([
            'success' => true,
            'data' => $sortedActivities
        ]);
    }
}
