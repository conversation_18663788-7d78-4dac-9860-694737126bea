<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ThemeSetting;
use Illuminate\Support\Facades\Validator;

class ThemeSettingController extends Controller
{
    /**
     * Get current theme settings (public)
     */
    public function getThemeSettings()
    {
        try {
            $theme = ThemeSetting::getCurrent();

            return response()->json([
                'success' => true,
                'data' => [
                    'colors' => [
                        'topNavBg' => $theme->top_nav_bg,
                        'topNavText' => $theme->top_nav_text,
                        'topNavIconColor' => $theme->top_nav_icon_color,
                        'mainNavBg' => $theme->main_nav_bg,
                        'mainNavText' => $theme->main_nav_text,
                        'mainNavHover' => $theme->main_nav_hover,
                    ],
                    'socialMedia' => $theme->getSocialMediaUrls(),
                    'cssVariables' => $theme->toCssVariables(),
                    'themeName' => $theme->theme_name,
                    'description' => $theme->description
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil pengaturan theme',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all theme settings for admin
     */
    public function index()
    {
        try {
            $settings = ThemeSetting::ordered()->get();

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data pengaturan theme',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store new theme setting
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'component_name' => 'required|string|max:255',
            'element_name' => 'required|string|max:255',
            'color_type' => 'required|string|max:255',
            'color_value' => 'required|string',
            'css_property' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'sort_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            // Check for unique constraint
            $existingRecord = ThemeSetting::where('component_name', $request->component_name)
                ->where('element_name', $request->element_name)
                ->where('color_type', $request->color_type)
                ->first();

            if ($existingRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kombinasi komponen, elemen, dan tipe warna sudah ada',
                    'errors' => [
                        'combination' => ['Kombinasi komponen, elemen, dan tipe warna sudah ada']
                    ]
                ], 400);
            }

            $data = $request->only([
                'component_name', 'element_name', 'color_type', 'color_value',
                'css_property', 'description', 'sort_order', 'is_active'
            ]);

            // Set default values
            $data['css_property'] = $data['css_property'] ?? 'background-color';
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_active'] = $data['is_active'] ?? true;

            $setting = ThemeSetting::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan theme berhasil ditambahkan',
                'data' => $setting
            ], 201);
        } catch (\Exception $e) {
            \Log::error('Theme setting store error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal menambahkan pengaturan theme',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific theme setting
     */
    public function show($id)
    {
        try {
            $setting = ThemeSetting::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $setting
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Pengaturan theme tidak ditemukan',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update theme setting
     */
    public function update(Request $request, $id)
    {
        try {
            $setting = ThemeSetting::findOrFail($id);

            // Create validation rules with unique constraint exception for current record
            $validator = Validator::make($request->all(), [
                'component_name' => 'required|string|max:255',
                'element_name' => 'required|string|max:255',
                'color_type' => 'required|string|max:255',
                'color_value' => 'required|string',
                'css_property' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'sort_order' => 'nullable|integer',
                'is_active' => 'nullable|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Custom hex color validation
            if (!$this->isValidHexColor($request->color_value)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Format warna tidak valid',
                    'errors' => [
                        'color_value' => ['Format warna harus berupa hex color (contoh: #3B82F6)']
                    ]
                ], 400);
            }

            // Custom hex color validation
            if (!$this->isValidHexColor($request->color_value)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Format warna tidak valid',
                    'errors' => [
                        'color_value' => ['Format warna harus berupa hex color (contoh: #3B82F6)']
                    ]
                ], 400);
            }

            // Check for unique constraint manually (excluding current record)
            $existingRecord = ThemeSetting::where('component_name', $request->component_name)
                ->where('element_name', $request->element_name)
                ->where('color_type', $request->color_type)
                ->where('id', '!=', $id)
                ->first();

            if ($existingRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'Kombinasi komponen, elemen, dan tipe warna sudah ada',
                    'errors' => [
                        'combination' => ['Kombinasi komponen, elemen, dan tipe warna sudah ada']
                    ]
                ], 400);
            }

            $data = $request->only([
                'component_name', 'element_name', 'color_type', 'color_value',
                'css_property', 'description', 'sort_order', 'is_active'
            ]);

            // Set default values
            $data['css_property'] = $data['css_property'] ?? 'background-color';
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_active'] = $data['is_active'] ?? true;

            $setting->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan theme berhasil diupdate',
                'data' => $setting->fresh()
            ]);
        } catch (\Exception $e) {
            \Log::error('Theme setting update error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupdate pengaturan theme',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete theme setting
     */
    public function destroy($id)
    {
        try {
            $setting = ThemeSetting::findOrFail($id);
            $setting->delete();

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan theme berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus pengaturan theme',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate hex color format
     */
    private function isValidHexColor($color)
    {
        // Remove # if present
        $color = ltrim($color, '#');

        // Check if it's 3 or 6 characters and all are hex
        return (strlen($color) === 3 || strlen($color) === 6) && ctype_xdigit($color);
    }

    /**
     * Update theme settings (admin only)
     */
    public function updateThemeSettings(Request $request)
    {
        try {
            // Validation
            $validator = Validator::make($request->all(), [
                'topNavBg' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'topNavText' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'topNavIconColor' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'mainNavBg' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'mainNavText' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'mainNavHover' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'socialMedia.instagram' => 'nullable|url',
                'socialMedia.youtube' => 'nullable|url',
                'socialMedia.facebook' => 'nullable|url',
                'socialMedia.twitter' => 'nullable|url',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            // Deactivate all existing themes
            ThemeSetting::where('is_active', true)->update(['is_active' => false]);

            // Create new theme
            $theme = ThemeSetting::create([
                'top_nav_bg' => $request->topNavBg,
                'top_nav_text' => $request->topNavText,
                'top_nav_icon_color' => $request->topNavIconColor,
                'main_nav_bg' => $request->mainNavBg,
                'main_nav_text' => $request->mainNavText,
                'main_nav_hover' => $request->mainNavHover,
                'instagram_url' => $request->input('socialMedia.instagram'),
                'youtube_url' => $request->input('socialMedia.youtube'),
                'facebook_url' => $request->input('socialMedia.facebook'),
                'twitter_url' => $request->input('socialMedia.twitter'),
                'is_active' => true,
                'theme_name' => 'Custom Theme',
                'description' => 'Custom theme created from admin panel'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Theme settings updated successfully',
                'data' => [
                    'colors' => [
                        'topNavBg' => $theme->top_nav_bg,
                        'topNavText' => $theme->top_nav_text,
                        'topNavIconColor' => $theme->top_nav_icon_color,
                        'mainNavBg' => $theme->main_nav_bg,
                        'mainNavText' => $theme->main_nav_text,
                        'mainNavHover' => $theme->main_nav_hover,
                    ],
                    'socialMedia' => $theme->getSocialMediaUrls(),
                    'cssVariables' => $theme->toCssVariables()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }
}
