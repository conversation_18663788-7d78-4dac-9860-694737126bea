<?php

namespace App\Http\Controllers;

use App\Models\SchoolLogo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class SchoolLogoController extends Controller
{
    /**
     * Get current active logo
     */
    public function getCurrentLogo()
    {
        try {
            $logo = DB::table('school_logos')
                     ->where('is_active', 1)
                     ->orderBy('created_at', 'desc')
                     ->first();

            if (!$logo) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active logo found',
                    'data' => null
                ]);
            }

            // Generate URL for frontend
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $logoUrl = "{$frontendUrl}/{$logo->file_path}";

            return response()->json([
                'success' => true,
                'message' => 'Logo retrieved successfully',
                'data' => [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'original_name' => $logo->original_name,
                    'url' => $logoUrl,
                    'mime_type' => $logo->mime_type,
                    'file_size' => $logo->file_size,
                    'formatted_size' => $this->formatFileSize($logo->file_size),
                    'is_active' => $logo->is_active,
                    'description' => $logo->description,
                    'uploaded_at' => $logo->created_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving logo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get latest logo (most recent upload)
     */
    public function getLatestLogo()
    {
        try {
            $logo = DB::table('school_logos')
                     ->orderBy('created_at', 'desc')
                     ->first();

            if (!$logo) {
                return response()->json([
                    'success' => false,
                    'message' => 'No logo found',
                    'data' => null
                ]);
            }

            // Generate URL for frontend
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $logoUrl = "{$frontendUrl}/{$logo->file_path}";

            return response()->json([
                'success' => true,
                'message' => 'Latest logo retrieved successfully',
                'data' => [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'original_name' => $logo->original_name,
                    'url' => $logoUrl,
                    'mime_type' => $logo->mime_type,
                    'file_size' => $logo->file_size,
                    'formatted_size' => $this->formatFileSize($logo->file_size),
                    'is_active' => $logo->is_active,
                    'description' => $logo->description,
                    'uploaded_at' => $logo->created_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving latest logo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload new logo
     */
    public function uploadLogo(Request $request)
    {
        try {
            if (!$request->hasFile('logo')) {
                return response()->json([
                    'success' => false,
                    'message' => 'No file uploaded'
                ], 400);
            }

            // Simple validation
            $request->validate([
                'logo' => 'required|image|mimes:jpeg,jpg,png,svg|max:2048'
            ]);

            $file = $request->file('logo');

            // Generate unique filename: school_logo_timestamp.ext
            $timestamp = time();
            $extension = $file->getClientOriginalExtension();
            $filename = "school_logo_{$timestamp}.{$extension}";

            // Define frontend logo path
            $frontendLogoPath = base_path('../frontend/public/images/logo');

            // Create directory if it doesn't exist
            if (!is_dir($frontendLogoPath)) {
                mkdir($frontendLogoPath, 0755, true);
            }

            // Move file to frontend public folder
            $destinationPath = $frontendLogoPath . '/' . $filename;
            $file->move($frontendLogoPath, $filename);

            // Deactivate all existing logos
            DB::table('school_logos')->update(['is_active' => 0]);

            // Insert new logo record directly
            $logoId = DB::table('school_logos')->insertGetId([
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'file_path' => "images/logo/{$filename}", // Relative path from frontend public
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'is_active' => 1,
                'description' => $request->input('description', 'Logo sekolah'),
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Get the inserted logo
            $logo = DB::table('school_logos')->where('id', $logoId)->first();

            // Generate URL for frontend (check both common ports)
            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');
            $logoUrl = "{$frontendUrl}/{$logo->file_path}";

            return response()->json([
                'success' => true,
                'message' => 'Logo uploaded successfully',
                'data' => [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'original_name' => $logo->original_name,
                    'url' => $logoUrl,
                    'file_path' => $logo->file_path,
                    'mime_type' => $logo->mime_type,
                    'file_size' => $logo->file_size,
                    'formatted_size' => $this->formatFileSize($logo->file_size),
                    'is_active' => $logo->is_active,
                    'description' => $logo->description,
                    'uploaded_at' => $logo->created_at
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading logo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all logos
     */
    public function getAllLogos()
    {
        try {
            $logos = DB::table('school_logos')
                      ->orderBy('created_at', 'desc')
                      ->get();

            $frontendUrl = env('FRONTEND_URL', 'http://localhost:5174');

            $logoData = $logos->map(function ($logo) use ($frontendUrl) {
                return [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'original_name' => $logo->original_name,
                    'url' => "{$frontendUrl}/{$logo->file_path}",
                    'mime_type' => $logo->mime_type,
                    'file_size' => $logo->file_size,
                    'formatted_size' => $this->formatFileSize($logo->file_size),
                    'is_active' => $logo->is_active,
                    'description' => $logo->description,
                    'uploaded_at' => $logo->created_at
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Logos retrieved successfully',
                'data' => $logoData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving logos: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format file size
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }



    /**
     * Delete logo
     */
    public function deleteLogo($id)
    {
        try {
            $logo = SchoolLogo::findOrFail($id);
            
            // Don't allow deleting if it's the only logo
            $totalLogos = SchoolLogo::count();
            if ($totalLogos <= 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete the only logo'
                ], 400);
            }

            // If deleting active logo, set another logo as active
            if ($logo->is_active) {
                $nextLogo = SchoolLogo::where('id', '!=', $id)
                                     ->orderBy('created_at', 'desc')
                                     ->first();
                if ($nextLogo) {
                    $nextLogo->setAsActive();
                }
            }

            $logo->delete(); // This will also delete the file (see boot method in model)

            return response()->json([
                'success' => true,
                'message' => 'Logo deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting logo: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set logo as active
     */
    public function setActiveLogo($id)
    {
        try {
            $logo = SchoolLogo::findOrFail($id);
            $logo->setAsActive();

            return response()->json([
                'success' => true,
                'message' => 'Logo set as active successfully',
                'data' => [
                    'id' => $logo->id,
                    'filename' => $logo->filename,
                    'url' => $logo->url,
                    'is_active' => $logo->is_active
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error setting logo as active: ' . $e->getMessage()
            ], 500);
        }
    }
}
