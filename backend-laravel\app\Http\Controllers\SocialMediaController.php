<?php

namespace App\Http\Controllers;

use App\Models\SocialMedia;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SocialMediaController extends Controller
{
    /**
     * Display a listing of social media.
     */
    public function index()
    {
        try {
            $socialMedia = SocialMedia::orderBy('sort_order', 'asc')
                                    ->orderBy('created_at', 'desc')
                                    ->get();

            return response()->json([
                'success' => true,
                'data' => $socialMedia
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data sosial media',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display active social media for public use.
     */
    public function getActive()
    {
        try {
            $socialMedia = SocialMedia::where('is_active', true)
                                    ->orderBy('sort_order', 'asc')
                                    ->orderBy('created_at', 'desc')
                                    ->get();

            return response()->json([
                'success' => true,
                'data' => $socialMedia
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data sosial media',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created social media.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'platform' => 'required|string|max:50',
            'name' => 'required|string|max:100',
            'url' => 'required|url|max:255',
            'icon_class' => 'nullable|string|max:100',
            'icon_color' => 'nullable|string|max:7',
            'text_color' => 'nullable|string|max:7',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $socialMedia = SocialMedia::create([
                'platform' => $request->platform,
                'name' => $request->name,
                'url' => $request->url,
                'icon_class' => $request->icon_class,
                'icon_color' => $request->icon_color ?? '#000000',
                'text_color' => $request->text_color ?? '#000000',
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->integer('sort_order', 0),
                'created_by' => auth()->id() ?? 1
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Sosial media berhasil ditambahkan',
                'data' => $socialMedia
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menambahkan sosial media',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified social media.
     */
    public function show($id)
    {
        try {
            $socialMedia = SocialMedia::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $socialMedia
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sosial media tidak ditemukan',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified social media.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'platform' => 'required|string|max:50',
            'name' => 'required|string|max:100',
            'url' => 'required|url|max:255',
            'icon_class' => 'nullable|string|max:100',
            'icon_color' => 'nullable|string|max:7',
            'text_color' => 'nullable|string|max:7',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $socialMedia = SocialMedia::findOrFail($id);
            
            $socialMedia->update([
                'platform' => $request->platform,
                'name' => $request->name,
                'url' => $request->url,
                'icon_class' => $request->icon_class,
                'icon_color' => $request->icon_color ?? '#000000',
                'text_color' => $request->text_color ?? '#000000',
                'is_active' => $request->boolean('is_active', true),
                'sort_order' => $request->integer('sort_order', 0),
                'updated_by' => auth()->id() ?? 1
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Sosial media berhasil diupdate',
                'data' => $socialMedia
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupdate sosial media',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified social media.
     */
    public function destroy($id)
    {
        try {
            $socialMedia = SocialMedia::findOrFail($id);
            $socialMedia->delete();

            return response()->json([
                'success' => true,
                'message' => 'Sosial media berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus sosial media',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle active status of social media.
     */
    public function toggleActive($id)
    {
        try {
            $socialMedia = SocialMedia::findOrFail($id);
            $socialMedia->is_active = !$socialMedia->is_active;
            $socialMedia->updated_by = auth()->id() ?? 1;
            $socialMedia->save();

            return response()->json([
                'success' => true,
                'message' => 'Status sosial media berhasil diubah',
                'data' => $socialMedia
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengubah status sosial media',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
