import React, { useState, Component, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import { useSchoolSettings } from '../hooks/useSchoolSettings';
import api from '../services/api';

// Error Boundary Component
class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    // You can log the error to an error reporting service here
    console.error('Contact Component Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              <svg
                className="mx-auto h-12 w-12 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
              <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                Oops! Something went wrong
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                We're sorry, but there was an error loading the contact form.
                Please try refreshing the page or contact support if the problem persists.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Validation schema
const contactSchema = yup.object().shape({
  name: yup
    .string()
    .required('Nama harus diisi')
    .min(2, 'Nama minimal 2 karakter')
    .max(100, 'Nama maksimal 100 karakter'),
  phone: yup
    .string()
    .required('Nomor WhatsApp harus diisi')
    .matches(/^(\+62|62|0)8[1-9][0-9]{6,9}$/, 'Format nomor WhatsApp tidak valid'),
  email: yup
    .string()
    .email('Format email tidak valid')
    .required('Email harus diisi'),
  subject: yup
    .string()
    .required('Subjek harus diisi')
    .min(5, 'Subjek minimal 5 karakter')
    .max(200, 'Subjek maksimal 200 karakter'),
  message: yup
    .string()
    .required('Pesan harus diisi')
    .min(10, 'Pesan minimal 10 karakter')
    .max(1000, 'Pesan maksimal 1000 karakter'),
  category: yup
    .string()
    .required('Kategori harus dipilih')
    .oneOf(['Pendaftaran', 'Akademik', 'Administrasi', 'Fasilitas', 'Ekstrakurikuler', 'Lainnya'], 'Kategori tidak valid')
});

// Google Maps Component using simple embed (no API key required)
const GoogleMapsComponent = ({ schoolName, schoolAddress }) => {
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState(false);

  // Real-time smart search with keyword detection for instant location targeting
  const createSearchQuery = () => {
    if (!schoolName && !schoolAddress) return '';

    const cleanSchoolName = schoolName ? schoolName.trim() : '';
    const cleanAddress = schoolAddress ? schoolAddress.trim() : '';

    // Database of known Indonesian schools and their exact locations
    const knownSchools = {
      // Jakarta
      'sma negeri 1 jakarta': 'SMA Negeri 1 Jakarta, Jl. Budi Kemuliaan I No.2, Jakarta Pusat',
      'sma negeri 3 jakarta': 'SMA Negeri 3 Jakarta, Jl. Setiabudi Raya, Jakarta Selatan',
      'smk negeri 1 jakarta': 'SMK Negeri 1 Jakarta, Jl. Budi Kemuliaan I, Jakarta Pusat',

      // Bandung
      'sma negeri 1 bandung': 'SMA Negeri 1 Bandung, Jl. Ir. H. Juanda No.93, Bandung',
      'sma negeri 3 bandung': 'SMA Negeri 3 Bandung, Jl. Belitung No.8, Bandung',
      'smk negeri 1 bandung': 'SMK Negeri 1 Bandung, Jl. Wastukancana No.3, Bandung',

      // Yogyakarta
      'sma negeri 1 yogyakarta': 'SMA Negeri 1 Yogyakarta, Jl. HOS Cokroaminoto No.10, Yogyakarta',
      'sma negeri 3 yogyakarta': 'SMA Negeri 3 Yogyakarta, Jl. Yos Sudarso No.7, Yogyakarta',

      // Surabaya
      'sma negeri 1 surabaya': 'SMA Negeri 1 Surabaya, Jl. Wijaya Kusuma No.48, Surabaya',
      'sma negeri 5 surabaya': 'SMA Negeri 5 Surabaya, Jl. Kusuma Bangsa No.21, Surabaya',

      // Semarang
      'sma negeri 1 semarang': 'SMA Negeri 1 Semarang, Jl. Taman Sari No.1, Semarang',
      'sma negeri 3 semarang': 'SMA Negeri 3 Semarang, Jl. Pemuda No.149, Semarang',
    };

    // Real-time keyword matching for instant location
    const schoolKey = cleanSchoolName.toLowerCase();

    // Check for exact match in known schools database
    if (knownSchools[schoolKey]) {
      return knownSchools[schoolKey];
    }

    // Smart partial matching for similar schools
    const partialMatch = Object.keys(knownSchools).find(key => {
      const keyWords = key.split(' ');
      const nameWords = schoolKey.split(' ');

      // Check if at least 70% of words match
      const matchCount = nameWords.filter(word =>
        keyWords.some(keyWord => keyWord.includes(word) || word.includes(keyWord))
      ).length;

      return matchCount / nameWords.length >= 0.7;
    });

    if (partialMatch) {
      return knownSchools[partialMatch];
    }

    // Advanced search strategies for unknown schools
    let bestQuery = '';

    if (cleanSchoolName && cleanAddress) {
      // Extract city from address for precise targeting
      const cityPatterns = [
        /jakarta/i, /bandung/i, /yogyakarta/i, /yogya/i, /surabaya/i,
        /semarang/i, /medan/i, /palembang/i, /makassar/i, /denpasar/i,
        /malang/i, /solo/i, /bogor/i, /depok/i, /tangerang/i, /bekasi/i
      ];

      const detectedCity = cityPatterns.find(pattern =>
        pattern.test(cleanAddress) || pattern.test(cleanSchoolName)
      );

      if (detectedCity) {
        const cityName = cleanAddress.match(detectedCity)?.[0] || cleanSchoolName.match(detectedCity)?.[0];
        bestQuery = `${cleanSchoolName} ${cityName} Indonesia`;
      } else {
        bestQuery = `${cleanSchoolName} ${cleanAddress}`;
      }
    } else if (cleanSchoolName) {
      // Add school context if missing
      const schoolTypes = ['sma', 'smk', 'smp', 'sd', 'sekolah'];
      const hasSchoolType = schoolTypes.some(type =>
        cleanSchoolName.toLowerCase().includes(type)
      );

      if (!hasSchoolType) {
        bestQuery = `sekolah ${cleanSchoolName} Indonesia`;
      } else {
        bestQuery = `${cleanSchoolName} Indonesia`;
      }
    } else if (cleanAddress) {
      bestQuery = `${cleanAddress} Indonesia`;
    }

    return bestQuery;
  };

  const searchQuery = createSearchQuery();
  const encodedQuery = encodeURIComponent(searchQuery);

  // Real-time Google Maps URL with precise targeting
  const mapEmbedUrl = `https://maps.google.com/maps?q=${encodedQuery}&output=embed&z=17&hl=id&t=m&iwloc=near`;

  const handleMapLoad = () => {
    setMapLoaded(true);
    setMapError(false);
  };

  const handleMapError = () => {
    console.log('Map loading error, but this is often normal for Google Maps embed');
    // Don't set error immediately, give it time to load
    setTimeout(() => {
      if (!mapLoaded) {
        setMapError(true);
      }
    }, 5000);
  };

  // Real-time update when school data changes
  useEffect(() => {
    if (schoolName || schoolAddress) {
      setMapLoaded(false);
      setMapError(false);
      console.log('🗺️ Real-time map update:', { schoolName, schoolAddress });
      console.log('🎯 Search query:', searchQuery);
    }
  }, [schoolName, schoolAddress, searchQuery]);

  if (mapError) {
    return (
      <div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center p-8">
        <svg className="w-16 h-16 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Gagal Memuat Peta</h3>
        <p className="text-gray-600 text-center mb-2">
          Maaf, peta tidak dapat dimuat saat ini.
        </p>
        <p className="text-sm text-gray-500 text-center mb-4">
          📍 Lokasi: {schoolName}<br />
          📍 Alamat: {schoolAddress}
        </p>
        <div className="space-y-2">
          <a
            href={`https://www.google.com/maps/search/${encodedQuery}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            Buka di Google Maps
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full bg-white rounded-lg overflow-hidden">
      {searchQuery ? (
        <>
          <iframe
            src={mapEmbedUrl}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen=""
            loading="eager"
            referrerPolicy="no-referrer-when-downgrade"
            title={`Peta Lokasi ${schoolName}`}
            onLoad={handleMapLoad}
            onError={handleMapError}
            className="w-full h-full"
            key={`school-location-${schoolName}-${schoolAddress}`}
          />
          {!mapLoaded && (
            <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
              <div className="text-center px-4">
                <div className="relative mb-4">
                  <div className="rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                  </div>
                </div>
                <p className="text-gray-600 font-medium">Memuat lokasi sekolah...</p>
                <p className="text-sm text-blue-600 mt-2 font-medium">📍 {schoolName}</p>
                <p className="text-xs text-gray-500 mt-1">{schoolAddress}</p>
                <div className="mt-4 text-xs text-gray-400">
                  <span className="inline-flex items-center">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    Menampilkan pin lokasi...
                  </span>
                </div>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
          <div className="text-center px-4">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <p className="text-gray-600 font-medium">Informasi lokasi tidak tersedia</p>
            <p className="text-sm text-gray-500 mt-2">Silakan hubungi sekolah untuk informasi lokasi</p>
          </div>
        </div>
      )}
    </div>
  );
};

const ContactForm = () => {
  const [submitStatus, setSubmitStatus] = useState(null);
  const { settings, loading: settingsLoading } = useSchoolSettings();

  // Error throwing test for development
  if (!settings && !settingsLoading) {
    throw new Error('Settings failed to load');
  }

  const categories = ['Pendaftaran', 'Akademik', 'Administrasi', 'Fasilitas', 'Ekstrakurikuler', 'Lainnya'];

  // React Hook Form
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset,
  } = useForm({
    resolver: yupResolver(contactSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      subject: '',
      message: '',
      category: ''
    }
  });

  const handleFormSubmit = async (data) => {
    setSubmitStatus(null);

    try {
      // Kirim data ke backend API
      const response = await api.post('/contacts', {
        name: data.name,
        phone: data.phone,
        email: data.email,
        subject: data.subject,
        message: data.message,
        category: data.category
      });

      if (response.data.success) {
        // Reset form setelah berhasil
        reset();
        setSubmitStatus('success');

        // Auto hide success message after 10 seconds
        setTimeout(() => {
          setSubmitStatus(null);
        }, 10000);
      } else {
        throw new Error(response.data.message || 'Gagal mengirim pesan');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setSubmitStatus('error');

      // Auto hide error message after 10 seconds
      setTimeout(() => {
        setSubmitStatus(null);
      }, 10000);
    }
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Hubungi Kami
        </h1>
        <p className="text-xl text-gray-600">
          Kami siap membantu Anda. Jangan ragu untuk menghubungi kami via WhatsApp
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Contact Form */}
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Kirim Pesan via WhatsApp</h2>

          {submitStatus === 'success' && (
            <div className="mb-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-green-800 font-semibold text-lg">Pesan Berhasil Dikirim!</h3>
                    <p className="text-green-700 text-sm mt-1">
                      Pesan Anda telah berhasil tersimpan dan diteruskan ke admin sekolah.
                    </p>
                    <div className="mt-3 space-y-2">
                      <div className="flex items-center text-green-600 text-sm">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Admin akan segera merespons pesan Anda
                      </div>
                      <div className="flex items-center text-green-600 text-sm">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Kami akan menghubungi Anda melalui WhatsApp atau email
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setSubmitStatus(null)}
                  className="flex-shrink-0 text-green-400 hover:text-green-600 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {submitStatus === 'error' && (
            <div className="mb-6 bg-gradient-to-r from-red-50 to-rose-50 border border-red-200 rounded-lg p-6 shadow-sm">
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-red-800 font-semibold text-lg">Terjadi Kesalahan!</h3>
                    <p className="text-red-700 text-sm mt-1">
                      Maaf, pesan Anda tidak dapat dikirim saat ini. Mohon coba lagi beberapa saat.
                    </p>
                    <div className="mt-3 flex items-center text-red-600 text-sm">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      Atau hubungi kami langsung via telepon
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => setSubmitStatus(null)}
                  className="flex-shrink-0 text-red-400 hover:text-red-600 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nama Lengkap *
                </label>
                <input
                  id="contact-form-name"
                  name="contact-form-name"
                  type="text"
                  {...register('name')}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300 ${
                    formErrors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Masukkan nama lengkap Anda"
                />
                {formErrors.name && (
                  <p className="text-red-600 text-sm mt-1 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {formErrors.name.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nomor WhatsApp *
                </label>
                <input
                  type="text"
                  {...register('phone')}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300 ${
                    formErrors.phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="081234567890"
                />
                {formErrors.phone && (
                  <p className="text-red-600 text-sm mt-1 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {formErrors.phone.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email *
              </label>
              <input
                type="email"
                {...register('email')}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300 ${
                  formErrors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
              />
              {formErrors.email && (
                <p className="text-red-600 text-sm mt-1 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {formErrors.email.message}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori *
                </label>
                <select
                  {...register('category')}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300 ${
                    formErrors.category ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Pilih Kategori</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {formErrors.category && (
                  <p className="text-red-600 text-sm mt-1 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {formErrors.category.message}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Subjek *
                </label>
                <input
                  type="text"
                  {...register('subject')}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300 ${
                    formErrors.subject ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Subjek pesan"
                />
                {formErrors.subject && (
                  <p className="text-red-600 text-sm mt-1 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {formErrors.subject.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pesan *
              </label>
              <textarea
                {...register('message')}
                rows={6}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-300 resize-vertical ${
                  formErrors.message ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Tulis pesan Anda di sini..."
              />
              {formErrors.message && (
                <p className="text-red-600 text-sm mt-1 flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {formErrors.message.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isSubmitting}
              className={`w-full py-3 px-6 rounded-lg font-semibold transition duration-300 flex items-center justify-center ${
                isSubmitting
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-200'
              } text-white`}
            >
              {isSubmitting ? (
                <>
                  <svg className="-ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Mengirim ke WhatsApp...
                </>
              ) : (
                <>
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.700"/>
                  </svg>
                  Kirim via WhatsApp
                </>
              )}
            </button>
          </form>
        </div>

        {/* Contact Information & Map */}
        <div className="space-y-8">
          {/* Contact Info */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Informasi Kontak</h2>
            
            <div className="space-y-6">
              {/* Alamat */}
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Alamat</h3>
                  <p className="text-gray-600 mt-1">
                    {settingsLoading ? 'Loading...' : settings.schoolAddress || 'Alamat belum tersedia'}
                  </p>
                </div>
              </div>

              {/* Telepon */}
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Telepon</h3>
                  <p className="text-gray-600 mt-1">
                    {settingsLoading ? 'Loading...' : settings.schoolPhone || 'Nomor telepon belum tersedia'}
                  </p>
                </div>
              </div>

              {/* Email */}
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-6 h-6 text-blue-600 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-gray-900">Email</h3>
                  <p className="text-gray-600 mt-1">
                    {settingsLoading ? 'Loading...' : settings.schoolEmail || 'Email belum tersedia'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Jam Operasional */}
          <div className="bg-white rounded-lg shadow-md p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Jam Operasional</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="font-medium text-gray-900">Senin - Jumat</span>
                <span className="text-gray-600">07:00 - 16:00 WIB</span>
              </div>
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="font-medium text-gray-900">Sabtu</span>
                <span className="text-gray-600">07:00 - 12:00 WIB</span>
              </div>
              <div className="flex justify-between items-center py-2">
                <span className="font-medium text-gray-900">Minggu</span>
                <span className="text-red-600 font-medium">Tutup</span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Catatan:</strong> Untuk keperluan mendesak di luar jam operasional, 
                silakan hubungi nomor darurat: (021) 123-4569
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Google Maps */}
      <div className="mt-12">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Lokasi Kami</h2>
            <p className="text-gray-600 mt-2">
              {settingsLoading ? 'Loading...' : `${settings.schoolName} - ${settings.schoolAddress}`}
            </p>
          </div>

          <div className="relative h-96">
            {!settingsLoading && settings.schoolName && settings.schoolAddress ? (
              <GoogleMapsComponent
                schoolName={settings.schoolName}
                schoolAddress={settings.schoolAddress}
              />
            ) : (
              <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                <div className="text-center">
                  <div className="rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Memuat informasi lokasi...</p>
                </div>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          {!settingsLoading && settings.schoolName && settings.schoolAddress && (
            <div className="p-4 bg-gray-50 border-t border-gray-200">
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <a
                  href={`https://www.google.com/maps/search/${encodeURIComponent(settings.schoolName + ' ' + settings.schoolAddress)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  Buka di Google Maps
                </a>
                <a
                  href={`https://www.google.com/maps/dir//${encodeURIComponent(settings.schoolName + ' ' + settings.schoolAddress)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                  </svg>
                  Dapatkan Petunjuk Arah
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const Contact = () => {
  return (
    <ErrorBoundary>
      <ContactForm />
    </ErrorBoundary>
  );
};

export default Contact;
