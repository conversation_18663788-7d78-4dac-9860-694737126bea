<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use App\Models\Setting;

class LogoControllerNew extends Controller
{
    /**
     * Upload logo file dengan path yang disesuaikan
     */
    public function upload(Request $request)
    {
        try {
            // Validasi file upload
            if (!$request->hasFile('logo')) {
                return response()->json([
                    'success' => false,
                    'error' => 'No file uploaded',
                    'message' => 'Silakan pilih file logo untuk diupload'
                ], 400);
            }

            $logoFile = $request->file('logo');
            
            // Validasi file
            if (!$logoFile->isValid()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file',
                    'message' => 'File yang diupload tidak valid'
                ], 400);
            }

            // Validasi tipe file
            $allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!in_array($logoFile->getMimeType(), $allowedMimes)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file type',
                    'message' => 'Hanya file JPEG, PNG, dan WebP yang diizinkan'
                ], 400);
            }

            // Validasi ukuran file (max 5MB)
            $maxSize = 5 * 1024 * 1024;
            if ($logoFile->getSize() > $maxSize) {
                return response()->json([
                    'success' => false,
                    'error' => 'File too large',
                    'message' => 'Ukuran file maksimal 5MB'
                ], 400);
            }
            
            // Generate nama file unik
            $timestamp = time();
            $extension = $logoFile->getClientOriginalExtension();
            $filename = "school_logo_{$timestamp}.{$extension}";
            
            // Path untuk menyimpan file - gunakan public/images/logo
            $uploadPath = public_path('images/logo');

            // Create directory if not exists
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move file to public directory
            $moved = $logoFile->move($uploadPath, $filename);

            if (!$moved) {
                return response()->json([
                    'success' => false,
                    'error' => 'Storage failed',
                    'message' => 'Gagal menyimpan file'
                ], 500);
            }

            // Generate URL untuk akses file
            $logoUrl = "http://localhost:8000/images/logo/{$filename}";
            $filePath = "/images/logo/{$filename}";
            
            // Simpan info logo ke settings
            $this->saveLogoToSettings($filename, $logoUrl);
            
            // Response sukses
            return response()->json([
                'success' => true,
                'message' => 'Logo berhasil diupload',
                'data' => [
                    'filename' => $filename,
                    'original_name' => $logoFile->getClientOriginalName(),
                    'file_path' => $filePath,
                    'file_size' => $logoFile->getSize(),
                    'formatted_size' => $this->formatBytes($logoFile->getSize()),
                    'mime_type' => $logoFile->getMimeType(),
                    'url' => $logoUrl,
                    'uploaded_at' => now()->format('Y-m-d H:i:s')
                ]
            ], 201);
            
        } catch (\Exception $e) {
            \Log::error('Logo upload error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => 'Upload failed',
                'message' => 'Terjadi kesalahan saat upload: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get current active logo
     */
    public function getCurrent()
    {
        try {
            // Ambil logo dari settings
            $logoUrl = $this->getLogoFromSettings();

            if (!$logoUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'Logo tidak ditemukan'
                ], 404);
            }

            // Extract filename dari URL
            $filename = basename(parse_url($logoUrl, PHP_URL_PATH));

            // Cek apakah file ada di public/images/logo
            $publicPath = public_path('images/logo/' . $filename);
            $fileExists = file_exists($publicPath);
            $fileSize = $fileExists ? filesize($publicPath) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'url' => $logoUrl,
                    'filename' => $filename,
                    'file_exists' => $fileExists,
                    'file_size' => $fileSize,
                    'formatted_size' => $this->formatBytes($fileSize),
                    'storage_path' => $publicPath
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get current logo',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Upload favicon file
     */
    public function uploadFavicon(Request $request)
    {
        try {
            if (!$request->hasFile('favicon')) {
                return response()->json([
                    'success' => false,
                    'error' => 'No file uploaded',
                    'message' => 'Silakan pilih file favicon untuk diupload'
                ], 400);
            }

            $faviconFile = $request->file('favicon');
            
            if (!$faviconFile->isValid()) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file',
                    'message' => 'File yang diupload tidak valid'
                ], 400);
            }

            // Validasi tipe file untuk favicon
            $allowedMimes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/jpeg'];
            if (!in_array($faviconFile->getMimeType(), $allowedMimes)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file type',
                    'message' => 'Favicon harus berformat .ico atau .png'
                ], 400);
            }

            // Validasi ukuran file (max 1MB untuk favicon)
            $maxSize = 1 * 1024 * 1024;
            if ($faviconFile->getSize() > $maxSize) {
                return response()->json([
                    'success' => false,
                    'error' => 'File too large',
                    'message' => 'Ukuran favicon maksimal 1MB'
                ], 400);
            }
            
            // Generate nama file unik
            $timestamp = time();
            $extension = $faviconFile->getClientOriginalExtension();
            $filename = "favicon_{$timestamp}.{$extension}";
            
            // Path untuk menyimpan favicon - gunakan public/images/logo
            $uploadPath = public_path('images/logo');

            // Create directory if not exists
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Move file to public directory
            $moved = $faviconFile->move($uploadPath, $filename);

            if (!$moved) {
                return response()->json([
                    'success' => false,
                    'error' => 'Storage failed',
                    'message' => 'Gagal menyimpan favicon'
                ], 500);
            }

            // Generate URL
            $faviconUrl = "http://localhost:8000/images/logo/{$filename}";
            $filePath = "/images/logo/{$filename}";
            
            // Simpan ke settings
            $this->saveFaviconToSettings($filename, $faviconUrl);
            
            return response()->json([
                'success' => true,
                'message' => 'Favicon berhasil diupload',
                'data' => [
                    'filename' => $filename,
                    'original_name' => $faviconFile->getClientOriginalName(),
                    'file_path' => $filePath,
                    'file_size' => $faviconFile->getSize(),
                    'formatted_size' => $this->formatBytes($faviconFile->getSize()),
                    'mime_type' => $faviconFile->getMimeType(),
                    'url' => $faviconUrl,
                    'uploaded_at' => now()->format('Y-m-d H:i:s')
                ]
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Upload failed',
                'message' => 'Terjadi kesalahan saat upload favicon: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get current favicon
     */
    public function getCurrentFavicon()
    {
        try {
            $faviconUrl = $this->getFaviconFromSettings();

            if (!$faviconUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'Favicon tidak ditemukan'
                ], 404);
            }

            $filename = basename(parse_url($faviconUrl, PHP_URL_PATH));
            $publicPath = public_path('images/logo/' . $filename);
            $fileExists = file_exists($publicPath);
            $fileSize = $fileExists ? filesize($publicPath) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'url' => $faviconUrl,
                    'filename' => $filename,
                    'file_exists' => $fileExists,
                    'file_size' => $fileSize,
                    'formatted_size' => $this->formatBytes($fileSize)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get current favicon',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simpan info logo ke settings
     */
    private function saveLogoToSettings($filename, $url)
    {
        Setting::updateOrCreate(
            ['key' => 'logoUrl'],
            [
                'value' => $url,
                'type' => 'string',
                'category' => 'general',
                'description' => 'URL logo sekolah',
                'is_public' => true,
                'updated_by' => auth()->id() ?? 1
            ]
        );
    }
    
    /**
     * Simpan info favicon ke settings
     */
    private function saveFaviconToSettings($filename, $url)
    {
        Setting::updateOrCreate(
            ['key' => 'faviconUrl'],
            [
                'value' => $url,
                'type' => 'string',
                'category' => 'appearance',
                'description' => 'URL favicon website',
                'is_public' => true,
                'updated_by' => auth()->id() ?? 1
            ]
        );
    }
    
    /**
     * Ambil logo dari settings
     */
    private function getLogoFromSettings()
    {
        $setting = Setting::where('key', 'logoUrl')->first();
        return $setting ? $setting->value : null;
    }
    
    /**
     * Ambil favicon dari settings
     */
    private function getFaviconFromSettings()
    {
        $setting = Setting::where('key', 'faviconUrl')->first();
        return $setting ? $setting->value : null;
    }
    
    /**
     * Format bytes ke format yang mudah dibaca
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
