<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Exception;

class NewsController extends Controller
{
    /**
     * Get published news (public)
     */
    public function index(Request $request)
    {
        $query = News::with('author:id,name')
            ->published()
            ->orderBy('published_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Featured filter
        if ($request->has('featured') && $request->featured) {
            $query->featured();
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Get single news by ID (public)
     */
    public function show($id)
    {
        $news = News::with('author:id,name')
            ->published()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $news
        ]);
    }

    /**
     * Increment views for a news item
     */
    public function incrementViews($id)
    {
        try {
            $news = News::findOrFail($id);
            $news->increment('views');

            return response()->json([
                'success' => true,
                'message' => 'Views incremented successfully',
                'views' => $news->views
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to increment views: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all news for admin (including unpublished)
     */
    public function adminIndex(Request $request)
    {
        $query = News::with('author:id,name')
            ->orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('published', false);
            }
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Create new news (admin only)
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'excerpt' => 'nullable|string',
                'category' => 'required|string',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'published' => 'required|boolean',
                'featured' => 'required|boolean',
                'status' => 'required|string|in:draft,published,archived',
                'tags' => 'nullable|string',
                'published_at' => 'nullable|date',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

            // Determine status and published state (sync logic)
            $status = $request->get('status', null);
            $published = $request->has('published') ? $request->boolean('published') : null;

            // Sync logic: status <-> published
            if ($status === 'published') {
                $published = true;
            } elseif ($status === 'draft' || $status === 'archived') {
                $published = false;
            } elseif ($published !== null) {
                // If published sent directly but status not set, set status accordingly
                $status = $published ? 'published' : 'draft';
            } else {
                // Default
                $status = 'draft';
                $published = false;
            }

            // Create news first
            $news = News::create([
                'title' => $request->title,
                'content' => $request->content,
                'excerpt' => $request->excerpt,
                'category' => $request->category,
                'image_url' => null,
                'author_id' => $request->user()->id ?? 1,
                'published' => $published,
                'featured' => $request->boolean('featured', false),
                'status' => $status,
                'tags' => $request->tags ?? [],
                'published_at' => $published ? now() : null,
            ]);

            // Handle image upload if present
            if ($request->hasFile('image')) {
                try {
                    $image = $request->file('image');

                    // Validate file
                    if (!$image->isValid()) {
                        throw new Exception('File yang diupload tidak valid');
                    }

                    // Generate unique filename
                    $timestamp = time();
                    $extension = $image->getClientOriginalExtension();
                    $filename = "news_{$news->id}_{$timestamp}.{$extension}";

                    // Save file to storage/app/public/news
                    $filePath = $image->storeAs('news', $filename, 'public');

                    if (!$filePath) {
                        throw new Exception('Gagal menyimpan file gambar');
                    }

                    // Generate URL untuk akses file
                    $imageUrl = asset('storage/' . $filePath);

                    // Update news dengan image URL
                    $news->image_url = $imageUrl;
                    $news->save();

                } catch (Exception $e) {
                    // Delete the created news if image upload fails
                    $news->delete();
                    throw new Exception('Error uploading image: ' . $e->getMessage());
                }
            }

            // Add author name to the response
            $news->author_name = $request->author;
            $news->load('author:id,name');

            return response()->json([
                'success' => true,
                'message' => 'News created successfully',
                'data' => $news
            ], 201);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update news (admin only)
     */
    public function update(Request $request, $id)
    {
        try {
            $news = News::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'sometimes|required|string|max:255',
                'content' => 'sometimes|required|string',
                'excerpt' => 'nullable|string',
                'category' => 'sometimes|required|string|max:255',
                'author' => 'sometimes|required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
                'published' => 'boolean',
                'featured' => 'boolean',
                'status' => 'nullable|in:draft,published,archived',
                'tags' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

            $updateData = $request->only(['title', 'content', 'excerpt', 'category', 'tags']);

            // Handle image deletion if requested
            $deleteImage = $request->boolean('delete_image', false);
            if ($deleteImage) {
                // Delete old image from storage if exists
                if ($news->image_url) {
                    $oldPath = str_replace(asset('storage/'), '', $news->image_url);
                    if (Storage::disk('public')->exists($oldPath)) {
                        Storage::disk('public')->delete($oldPath);
                    }
                }
                $updateData['image_url'] = null;
            }
            // Handle new image upload
            else if ($request->hasFile('image')) {
                try {
                    $image = $request->file('image');

                    // Validate file
                    if (!$image->isValid()) {
                        throw new Exception('File yang diupload tidak valid');
                    }
                    
                    // Delete old image from storage if exists
                    if ($news->image_url) {
                        $oldPath = str_replace(asset('storage/'), '', $news->image_url);
                        if (Storage::disk('public')->exists($oldPath)) {
                            Storage::disk('public')->delete($oldPath);
                        }
                    }
                    
                    // Generate filename unik
                    $timestamp = time();
                    $extension = $image->getClientOriginalExtension();
                    $filename = "news_{$news->id}_{$timestamp}.{$extension}";

                    // Save file to storage/app/public/news
                    $filePath = $image->storeAs('news', $filename, 'public');

                    if (!$filePath) {
                        throw new Exception('Gagal menyimpan file gambar');
                    }

                    // Generate URL untuk akses file
                    $imageUrl = asset('storage/' . $filePath);
                    $updateData['image_url'] = $imageUrl;

                } catch (Exception $e) {
                    return response()->json([
                        'success' => false,
                        'error' => 'Failed to update image: ' . $e->getMessage()
                    ], 500);
                }
            }

            $status = $request->has('status') ? $request->get('status') : null;
            $published = $request->has('published') ? $request->boolean('published') : null;

            if ($status === 'published') {
                $updateData['status'] = 'published';
                $updateData['published'] = true;
                if (!$news->published_at) {
                    $updateData['published_at'] = now();
                }
            } elseif ($status === 'draft' || $status === 'archived') {
                $updateData['status'] = $status;
                $updateData['published'] = false;
                $updateData['published_at'] = null;
            } elseif ($published !== null) {
                $updateData['published'] = $published;
                $updateData['status'] = $published ? 'published' : 'draft';
                if ($published && !$news->published_at) {
                    $updateData['published_at'] = now();
                }
                if (!$published) {
                    $updateData['published_at'] = null;
                }
            }

            if ($request->has('featured')) {
                $updateData['featured'] = $request->boolean('featured');
            }

            $news->update($updateData);
            $news->load('author:id,name');

            // Add author name to the response if provided
            if ($request->has('author')) {
                $news->author_name = $request->author;
            }

            return response()->json([
                'success' => true,
                'message' => 'News updated successfully',
                'data' => $news
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete news (admin only)
     */
    public function destroy($id)
    {
        try {
            $news = News::findOrFail($id);

            // Delete image file if exists from storage
            if ($news->image_url) {
                $filePath = str_replace(asset('storage/'), '', $news->image_url);
                if (Storage::disk('public')->exists($filePath)) {
                    Storage::disk('public')->delete($filePath);
                }
            }

            $news->delete();

            return response()->json([
                'success' => true,
                'message' => 'News deleted successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete news: ' . $e->getMessage()
            ], 500);
        }
    }
}
