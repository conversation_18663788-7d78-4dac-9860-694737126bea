<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Get all contact messages (public)
     */
    public function index(Request $request)
    {
        $query = Contact::orderBy('created_at', 'desc');

        // Category filter
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Status filter
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%")
                  ->orWhere('subject', 'like', "%{$request->search}%")
                  ->orWhere('message', 'like', "%{$request->search}%");
            });
        }

        $contacts = $query->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $contacts->items(),
            'pagination' => [
                'current_page' => $contacts->currentPage(),
                'last_page' => $contacts->lastPage(),
                'per_page' => $contacts->perPage(),
                'total' => $contacts->total(),
            ]
        ]);
    }

    /**
     * Get single contact message by ID (admin only)
     */
    public function show($id)
    {
        $contact = Contact::findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $contact
        ]);
    }

    /**
     * Get all contact messages for admin (including all statuses)
     */
    public function adminIndex(Request $request)
    {
        $query = Contact::orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->search}%")
                  ->orWhere('email', 'like', "%{$request->search}%")
                  ->orWhere('subject', 'like', "%{$request->search}%")
                  ->orWhere('message', 'like', "%{$request->search}%");
            });
        }

        // Category filter
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Status filter
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status', $request->status);
        }

        // Priority filter
        if ($request->has('priority') && $request->priority !== 'all') {
            $query->where('priority', $request->priority);
        }

        $contacts = $query->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $contacts->items(),
            'pagination' => [
                'current_page' => $contacts->currentPage(),
                'last_page' => $contacts->lastPage(),
                'per_page' => $contacts->perPage(),
                'total' => $contacts->total(),
            ]
        ]);
    }

    /**
     * Store new contact message (public)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
            'phone' => 'required|string|min:10|max:15',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:200',
            'message' => 'required|string|max:1000',
            'category' => 'required|in:Pendaftaran,Akademik,Administrasi,Fasilitas,Ekstrakurikuler,Lainnya',
            'priority' => 'nullable|in:Rendah,Sedang,Tinggi,Urgent',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $contact = Contact::create([
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'subject' => $request->subject,
            'message' => $request->message,
            'category' => $request->category,
            'priority' => $request->get('priority', 'Sedang'),
            'status' => 'Pending',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Contact message sent successfully',
            'data' => $contact
        ], 201);
    }

    /**
     * Update contact message status (admin only)
     */
    public function update(Request $request, $id)
    {
        $contact = Contact::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:100',
            'phone' => 'sometimes|required|string|min:10|max:15',
            'email' => 'sometimes|required|email|max:255',
            'subject' => 'sometimes|required|string|max:200',
            'message' => 'sometimes|required|string|max:1000',
            'category' => 'sometimes|required|in:Pendaftaran,Akademik,Administrasi,Fasilitas,Ekstrakurikuler,Lainnya',
            'priority' => 'sometimes|required|in:Rendah,Sedang,Tinggi,Urgent',
            'status' => 'sometimes|required|in:Pending,Responded,Resolved',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $updateData = [];
        
        // Update basic fields if provided
        foreach(['name', 'phone', 'email', 'subject', 'message', 'category', 'priority'] as $field) {
            if ($request->has($field)) {
                $updateData[$field] = $request->$field;
            }
        }

        // Handle status update
        if ($request->has('status')) {
            $updateData['status'] = $request->status;
            
            // Set responded_at when status changes to Responded
            if ($request->status === 'Responded' && $contact->status !== 'Responded') {
                $updateData['responded_at'] = now();
            }

            // Set resolved_at when status changes to Resolved
            if ($request->status === 'Resolved' && $contact->status !== 'Resolved') {
                $updateData['resolved_at'] = now();
            }
        }

        if ($request->has('admin_notes')) {
            $updateData['admin_notes'] = $request->admin_notes;
        }

        $contact->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Contact message updated successfully',
            'data' => $contact
        ]);
    }

    /**
     * Delete contact message (admin only)
     */
    public function destroy($id)
    {
        $contact = Contact::findOrFail($id);
        $contact->delete();

        return response()->json([
            'success' => true,
            'message' => 'Contact message deleted successfully'
        ]);
    }

    /**
     * Bulk update contact messages status (admin only)
     */
    public function bulkUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:contacts,id',
            'status' => 'required|in:Pending,Responded,Resolved',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $updateData = ['status' => $request->status];
        
        if ($request->status === 'Responded') {
            $updateData['responded_at'] = now();
        } elseif ($request->status === 'Resolved') {
            $updateData['resolved_at'] = now();
        }

        Contact::whereIn('id', $request->ids)->update($updateData);

        return response()->json([
            'success' => true,
            'message' => count($request->ids) . ' contacts updated successfully'
        ]);
    }

    /**
     * Bulk delete contact messages (admin only)
     */
    public function bulkDestroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ids' => 'required|array',
            'ids.*' => 'required|integer|exists:contacts,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        Contact::whereIn('id', $request->ids)->delete();

        return response()->json([
            'success' => true,
            'message' => count($request->ids) . ' contacts deleted successfully'
        ]);
    }
}
