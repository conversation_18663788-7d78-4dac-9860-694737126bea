<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Exception;

class NewsController extends Controller
{
    /**
     * Handle direct image upload
     */
    public function upload(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

            if (!$request->hasFile('image')) {
                return response()->json([
                    'success' => false,
                    'error' => 'No image file uploaded'
                ], 400);
            }

            $image = $request->file('image');

            // Generate unique filename
            $timestamp = time();
            $extension = $this->getProperExtension($image);
            $filename = "news_temp_{$timestamp}.{$extension}";

            // Save file to backend Laravel public/images/news directory
            $uploadPath = public_path('images/news');

            // Create directory if it doesn't exist
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if (!$image->move($uploadPath, $filename)) {
                throw new Exception('Failed to save image file');
            }

            return response()->json([
                'success' => true,
                'url' => "http://localhost:8000/images/news/{$filename}",
                'path' => "/images/news/{$filename}"
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get published news (public)
     */
    public function index(Request $request)
    {
        $query = News::with('author:id,name')
            ->published()
            ->orderBy('published_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Featured filter
        if ($request->has('featured') && $request->featured) {
            $query->featured();
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Get single news by ID (public)
     */
    public function show($id)
    {
        $news = News::with('author:id,name')
            ->published()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $news
        ]);
    }

    /**
     * Increment views for a news item
     */
    public function incrementViews($id)
    {
        try {
            $news = News::findOrFail($id);
            $news->increment('views');

            return response()->json([
                'success' => true,
                'message' => 'Views incremented successfully',
                'views' => $news->views
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to increment views: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all news for admin (including unpublished)
     */
    public function adminIndex(Request $request)
    {
        $query = News::with('author:id,name')
            ->orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('published', false);
            }
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Create new news (admin only)
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'excerpt' => 'nullable|string',
                'category' => 'required|string',
                'author' => 'nullable|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
                'image_url' => 'nullable|string',
                'published' => 'nullable|boolean',
                'featured' => 'nullable|boolean',
                'status' => 'nullable|string|in:draft,published,archived',
                'tags' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

            // Determine status and published state (sync logic)
            $status = $request->get('status', null);
            $published = $request->has('published') ? $request->boolean('published') : null;

            // Sync logic: status <-> published
            if ($status === 'published') {
                $published = true;
            } elseif ($status === 'draft' || $status === 'archived') {
                $published = false;
            } elseif ($published !== null) {
                // If published sent directly but status not set, set status accordingly
                $status = $published ? 'published' : 'draft';
            } else {
                // Default
                $status = 'draft';
                $published = false;
            }

            // Create news first
            $news = News::create([
                'title' => $request->title,
                'content' => $request->content,
                'excerpt' => $request->excerpt,
                'category' => $request->category,
                'image_url' => null,
                'author_id' => $request->user()->id ?? 1,
                'published' => $published,
                'featured' => $request->boolean('featured', false),
                'status' => $status,
                'tags' => $request->tags ?? [],
                'published_at' => $published ? now() : null,
            ]);

            // Handle file upload menggunakan Gallery logic - SAMA PERSIS
            if (!$request->hasFile('image')) {
                // Delete the created news if no image
                $news->delete();
                return response()->json([
                    'success' => false,
                    'error' => 'No image file',
                    'message' => 'Silakan pilih file gambar'
                ], 400);
            }

            $image = $request->file('image');

            // Validasi file - SAMA SEPERTI GALLERY
            if (!$image->isValid()) {
                $news->delete();
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid file',
                    'message' => 'File yang diupload tidak valid'
                ], 400);
            }

            // Generate filename dengan ID berita - gunakan helper untuk ekstensi yang benar
            $extension = $this->getProperExtension($image);
            $filename = "news_id_{$news->id}.{$extension}"; // Format: news_id_1.jpg

            // Save file to backend Laravel public/images/news directory
            $uploadPath = public_path('images/news');

            // Create directory if it doesn't exist
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            if (!$image->move($uploadPath, $filename)) {
                $news->delete();
                return response()->json([
                    'success' => false,
                    'error' => 'Storage failed',
                    'message' => 'Gagal menyimpan file gambar'
                ], 500);
            }

            // Generate URL untuk akses file dari backend
            $imageUrl = "http://localhost:8000/images/news/{$filename}";

            // Update news dengan image URL - SIMPAN KE DATABASE
            $news->image_url = $imageUrl;
            $news->save();

            // Auto-sync ke frontend public
            $this->syncImageToFrontend($filename);

            // Add author name to the response
            $news->author_name = $request->author;
            $news->load('author:id,name');

            return response()->json([
                'success' => true,
                'message' => 'News created successfully',
                'data' => $news
            ], 201);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update news (admin only)
     */
    public function update(Request $request, $id)
    {
        try {
            $news = News::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'sometimes|required|string|max:255',
                'content' => 'sometimes|required|string',
                'excerpt' => 'nullable|string',
                'category' => 'sometimes|required|string|max:255',
                'author' => 'sometimes|required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
                'published' => 'boolean',
                'featured' => 'boolean',
                'status' => 'nullable|in:draft,published,archived',
                'tags' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

            $updateData = $request->only(['title', 'content', 'excerpt', 'category', 'tags']);

            // IMPORTANT: Preserve existing image_url if no image changes are requested
            // This prevents image_url from being lost during status updates
            $preserveImage = true;

            // Explicitly preserve image_url unless we're changing it
            if ($news->image_url && $preserveImage) {
                $updateData['image_url'] = $news->image_url;
            }

            // Handle image deletion if requested
            $deleteImage = $request->boolean('delete_image', false);
            if ($deleteImage) {
                // Delete old image from storage if exists
                if ($news->image_url) {
                    $this->deleteOldImage($news->image_url);
                }
                $updateData['image_url'] = null;
                $preserveImage = false;
            }
            // Handle new image upload
            else if ($request->hasFile('image')) {
                $preserveImage = false;

                // Delete old image first if exists
                if ($news->image_url) {
                    $this->deleteOldImage($news->image_url);
                }
                try {
                    $image = $request->file('image');

                    // Validasi file - SAMA SEPERTI GALLERY
                    if (!$image->isValid()) {
                        return response()->json([
                            'success' => false,
                            'error' => 'Invalid file',
                            'message' => 'File yang diupload tidak valid'
                        ], 400);
                    }

                    // Generate filename dengan ID berita - gunakan helper untuk ekstensi yang benar
                    $extension = $this->getProperExtension($image);
                    $filename = "news_id_{$news->id}.{$extension}"; // Format: news_id_1.jpg

                    // Save file to backend Laravel public/images/news directory
                    $uploadPath = public_path('images/news');

                    // Create directory if it doesn't exist
                    if (!file_exists($uploadPath)) {
                        mkdir($uploadPath, 0755, true);
                    }

                    if (!$image->move($uploadPath, $filename)) {
                        return response()->json([
                            'success' => false,
                            'error' => 'Storage failed',
                            'message' => 'Gagal menyimpan file gambar'
                        ], 500);
                    }

                    // Generate URL untuk akses file dari backend
                    $imageUrl = "http://localhost:8000/images/news/{$filename}";
                    $updateData['image_url'] = $imageUrl; // SIMPAN KE DATABASE

                } catch (Exception $e) {
                    \Log::error('News update error: ' . $e->getMessage());

                    return response()->json([
                        'success' => false,
                        'error' => 'Upload failed',
                        'message' => 'Terjadi kesalahan saat upload: ' . $e->getMessage()
                    ], 500);
                }
            }

            // If no image changes were made, preserve the existing image_url
            if ($preserveImage && $news->image_url) {
                $updateData['image_url'] = $news->image_url;
            }

            $status = $request->has('status') ? $request->get('status') : null;
            $published = $request->has('published') ? $request->boolean('published') : null;

            if ($status === 'published') {
                $updateData['status'] = 'published';
                $updateData['published'] = true;
                if (!$news->published_at) {
                    $updateData['published_at'] = now();
                }
            } elseif ($status === 'draft' || $status === 'archived') {
                $updateData['status'] = $status;
                $updateData['published'] = false;
                $updateData['published_at'] = null;
            } elseif ($published !== null) {
                $updateData['published'] = $published;
                $updateData['status'] = $published ? 'published' : 'draft';
                if ($published && !$news->published_at) {
                    $updateData['published_at'] = now();
                }
                if (!$published) {
                    $updateData['published_at'] = null;
                }
            }

            if ($request->has('featured')) {
                $updateData['featured'] = $request->boolean('featured');
            }



            $news->update($updateData);
            $news->load('author:id,name');

            // Add author name to the response if provided
            if ($request->has('author')) {
                $news->author_name = $request->author;
            }



            return response()->json([
                'success' => true,
                'message' => 'News updated successfully',
                'data' => $news
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete news (admin only)
     */
    public function destroy($id)
    {
        try {
            $news = News::findOrFail($id);

            // Delete image file if exists from storage
            if ($news->image_url) {
                $filePath = str_replace(asset('storage/'), '', $news->image_url);
                if (Storage::disk('public')->exists($filePath)) {
                    Storage::disk('public')->delete($filePath);
                }
            }

            $news->delete();

            return response()->json([
                'success' => true,
                'message' => 'News deleted successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to delete old image file
     */
    private function deleteOldImage($imageUrl)
    {
        if (!$imageUrl) return;

        try {
            // Handle backend Laravel public/images/news path (NEW)
            if (strpos($imageUrl, 'localhost:8000/images/news/') !== false) {
                $filename = basename($imageUrl);
                $filePath = public_path('images/news/' . $filename);

                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle /images/news path (relative format)
            elseif (strpos($imageUrl, '/images/news/') === 0) {
                $filename = basename($imageUrl);
                $filePath = public_path('images/news/' . $filename);

                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle uploads/images/news path (old backend structure)
            elseif (strpos($imageUrl, '/uploads/images/news/') === 0) {
                $filename = basename($imageUrl);
                $filePath = base_path('../uploads/images/news/' . $filename);

                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            // Handle old uploads/images path (backward compatibility)
            elseif (strpos($imageUrl, '/uploads/images/') === 0) {
                $filename = basename($imageUrl);
                // Try news folder first
                $newsPath = base_path('../uploads/images/news/' . $filename);
                $oldPath = base_path('../uploads/images/' . $filename);

                if (file_exists($newsPath)) {
                    unlink($newsPath);
                } elseif (file_exists($oldPath)) {
                    unlink($oldPath);
                }
            }
            // Handle old storage path for backward compatibility
            elseif (strpos($imageUrl, '/storage/') === 0) {
                $relativePath = substr($imageUrl, 9); // Remove '/storage/'
                if (Storage::disk('public')->exists($relativePath)) {
                    Storage::disk('public')->delete($relativePath);
                }
            }
        } catch (Exception $e) {
            // Log error but don't fail the operation
            \Log::warning('Failed to delete old image: ' . $e->getMessage());
        }
    }

    /**
     * Sync image to frontend public directory (sama seperti Gallery)
     */
    private function syncImageToFrontend($filename)
    {
        try {
            $sourcePath = base_path('../uploads/images/news/' . $filename);
            $targetPath = base_path('../frontend/public/uploads/images/news/' . $filename);

            // Create target directory if it doesn't exist
            $targetDir = dirname($targetPath);
            if (!file_exists($targetDir)) {
                mkdir($targetDir, 0755, true);
            }

            // Copy file to frontend public
            if (file_exists($sourcePath)) {
                copy($sourcePath, $targetPath);
            }
        } catch (Exception $e) {
            \Log::warning('Failed to sync image to frontend: ' . $e->getMessage());
        }
    }

    /**
     * Async sync image to frontend (untuk speed)
     */
    private function syncImageToFrontendAsync($filename) {
        // Jalankan sync di background untuk tidak menghambat response
        register_shutdown_function(function() use ($filename) {
            $this->syncImageToFrontend($filename);
        });
    }

    /**
     * Get proper file extension from uploaded file
     */
    private function getProperExtension($file) {
        // Dapatkan ekstensi asli dari file
        $originalExtension = $file->getClientOriginalExtension();

        // Jika ekstensi kosong atau tidak valid, coba dari MIME type
        if (empty($originalExtension)) {
            $mimeType = $file->getMimeType();
            $extensionFromMime = $this->getExtensionFromMimeType($mimeType);
            if ($extensionFromMime) {
                return $extensionFromMime;
            }
        }

        // Pastikan ekstensi dalam lowercase dan valid
        $extension = strtolower($originalExtension);

        // Validasi ekstensi yang diizinkan
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (in_array($extension, $allowedExtensions)) {
            return $extension;
        }

        // Default fallback
        return 'jpg';
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType($mimeType) {
        $mimeToExtension = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];

        return $mimeToExtension[$mimeType] ?? null;
    }
}
