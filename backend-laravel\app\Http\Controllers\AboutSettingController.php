<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AboutSetting;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class AboutSettingController extends Controller
{
    /**
     * Get all about settings (public)
     */
    public function getAboutSettings()
    {
        try {
            $settings = AboutSetting::active()->ordered()->get();

            // Group by section_key for easier frontend consumption
            $groupedSettings = $settings->groupBy('section_key')->map(function ($items) {
                if ($items->count() === 1) {
                    return $items->first();
                }
                return $items;
            });

            return response()->json([
                'success' => true,
                'data' => $groupedSettings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil pengaturan about',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all about settings for admin
     */
    public function index()
    {
        try {
            $settings = AboutSetting::ordered()->get();

            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengambil data pengaturan about',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store new about setting
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'section_key' => 'required|string|max:255|unique:about_settings,section_key',
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_data' => 'nullable|string',
            'sort_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'meta_data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $data = $request->only(['section_key', 'title', 'content', 'description', 'icon', 'sort_order', 'is_active', 'meta_data']);

            // Handle image upload
            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $imageName = time() . '_' . $image->getClientOriginalName();

                // Create directory if it doesn't exist
                $aboutDir = public_path('image/about');
                if (!file_exists($aboutDir)) {
                    mkdir($aboutDir, 0755, true);
                }

                // Move file to public/image/about
                $image->move($aboutDir, $imageName);
                $data['image_url'] = '/image/about/' . $imageName;
            }

            // Handle additional_data
            if ($request->has('additional_data') && !empty($request->additional_data)) {
                try {
                    $data['additional_data'] = json_decode($request->additional_data, true);
                } catch (\Exception $e) {
                    $data['additional_data'] = $request->additional_data;
                }
            }

            // Set default values
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['is_active'] = $data['is_active'] ?? true;

            $setting = AboutSetting::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan about berhasil ditambahkan',
                'data' => $setting
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menambahkan pengaturan about',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show specific about setting
     */
    public function show($id)
    {
        try {
            $setting = AboutSetting::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $setting
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Pengaturan about tidak ditemukan',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update about setting
     */
    public function update(Request $request, $id)
    {
        \Log::info('AboutSetting update called', ['id' => $id, 'data' => $request->all()]);

        $validator = Validator::make($request->all(), [
            'section_key' => 'required|string|max:255|unique:about_settings,section_key,' . $id,
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_data' => 'nullable|string',
            'sort_order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
            'meta_data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $setting = AboutSetting::findOrFail($id);
            $data = $request->only(['section_key', 'title', 'content', 'description', 'icon', 'sort_order', 'is_active', 'meta_data']);

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($setting->image_url) {
                    $oldImagePath = public_path($setting->image_url);
                    if (file_exists($oldImagePath)) {
                        unlink($oldImagePath);
                    }
                }

                $image = $request->file('image');
                $imageName = time() . '_' . $image->getClientOriginalName();

                // Create directory if it doesn't exist
                $aboutDir = public_path('image/about');
                if (!file_exists($aboutDir)) {
                    mkdir($aboutDir, 0755, true);
                }

                // Move file to public/image/about
                $image->move($aboutDir, $imageName);
                $data['image_url'] = '/image/about/' . $imageName;
            }

            // Handle additional_data
            if ($request->has('additional_data') && !empty($request->additional_data)) {
                try {
                    $data['additional_data'] = json_decode($request->additional_data, true);
                } catch (\Exception $e) {
                    $data['additional_data'] = $request->additional_data;
                }
            }

            $setting->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan about berhasil diupdate',
                'data' => $setting->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengupdate pengaturan about',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete about setting
     */
    public function destroy($id)
    {
        try {
            $setting = AboutSetting::findOrFail($id);
            $setting->delete();

            return response()->json([
                'success' => true,
                'message' => 'Pengaturan about berhasil dihapus'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus pengaturan about',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
