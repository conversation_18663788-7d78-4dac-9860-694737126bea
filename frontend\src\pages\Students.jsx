import React, { useState } from 'react';

const Students = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Sample data untuk pengumuman
  const announcements = [
    {
      id: 1,
      title: 'Pengumuman Libur Semester Ganjil 2024/2025',
      category: 'akademik',
      date: '2025-01-15',
      priority: 'high',
      content: 'Libur semester ganjil akan dimulai pada tanggal 20 Januari 2025 dan masuk kembali pada tanggal 3 Februari 2025. Siswa diharapkan menggunakan waktu libur untuk belajar mandiri.',
      author: '<PERSON><PERSON><PERSON>',
      views: 245,
      isNew: true
    },
    {
      id: 2,
      title: 'Pendaftaran Ekstrakurikuler Semester Genap',
      category: 'ekstrakurikuler',
      date: '2025-01-10',
      priority: 'medium',
      content: 'Pendaftaran ekstrakurikuler untuk semester genap dibuka mulai tanggal 15 Januari 2025. Siswa dapat mendaftar maksimal 2 ekstrakurikuler.',
      author: '<PERSON><PERSON><PERSON>',
      views: 189,
      isNew: true
    },
    {
      id: 3,
      title: '<PERSON><PERSON><PERSON> (UTS)',
      category: 'akademik',
      date: '2025-01-08',
      priority: 'high',
      content: 'UTS akan dilaksanakan pada tanggal 25-29 Januari 2025. Siswa diharapkan mempersiapkan diri dengan baik dan mengikuti semua mata pelajaran yang diujikan.',
      author: 'Wakil Kepala Sekolah',
      views: 312,
      isNew: false
    },
    {
      id: 4,
      title: 'Lomba Karya Tulis Ilmiah Tingkat Sekolah',
      category: 'kompetisi',
      date: '2025-01-05',
      priority: 'medium',
      content: 'Lomba karya tulis ilmiah akan diadakan pada bulan Februari 2025. Pendaftaran dibuka hingga tanggal 20 Januari 2025.',
      author: 'Guru Pembimbing',
      views: 156,
      isNew: false
    },
    {
      id: 5,
      title: 'Perubahan Jadwal Pelajaran Sementara',
      category: 'akademik',
      date: '2025-01-03',
      priority: 'medium',
      content: 'Terdapat perubahan jadwal pelajaran sementara untuk kelas X, XI, dan XII mulai tanggal 10 Januari 2025 karena ada kegiatan workshop guru.',
      author: 'Tata Usaha',
      views: 203,
      isNew: false
    },
    {
      id: 6,
      title: 'Kegiatan Bakti Sosial Sekolah',
      category: 'kegiatan',
      date: '2024-12-28',
      priority: 'low',
      content: 'Kegiatan bakti sosial akan dilaksanakan pada tanggal 15 Februari 2025. Siswa yang berminat dapat mendaftar di ruang OSIS.',
      author: 'Pembina OSIS',
      views: 98,
      isNew: false
    }
  ];

  const categories = [
    {
      id: 'all',
      name: 'Semua',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
        </svg>
      )
    },
    {
      id: 'akademik',
      name: 'Akademik',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/>
        </svg>
      )
    },
    {
      id: 'ekstrakurikuler',
      name: 'Ekstrakurikuler',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      )
    },
    {
      id: 'kompetisi',
      name: 'Kompetisi',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V19C19 20.1 18.1 21 17 21H7C5.9 21 5 20.1 5 19V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V19H17V6H7ZM12 8L14.5 10.5L12 13L9.5 10.5L12 8Z"/>
        </svg>
      )
    },
    {
      id: 'kegiatan',
      name: 'Kegiatan',
      icon: (
        <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
        </svg>
      )
    }
  ];

  const filteredAnnouncements = selectedCategory === 'all' 
    ? announcements 
    : announcements.filter(announcement => announcement.category === selectedCategory);

  const formatDate = (dateString) => {
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      timeZone: 'Asia/Jakarta'
    };
    return new Date(dateString).toLocaleDateString('id-ID', options);
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'high': return 'Penting';
      case 'medium': return 'Sedang';
      case 'low': return 'Biasa';
      default: return 'Normal';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <svg className="w-8 h-8 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                </svg>
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900">
                Pengumuman Sekolah
              </h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Informasi terbaru dan pengumuman penting untuk seluruh siswa
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Filter - Responsive */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-sm p-4 md:p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Filter Kategori</h2>
            
            {/* Desktop Filter (>= 900px) */}
            <div className="hidden min-[900px]:flex flex-wrap gap-3">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="mr-2">{category.icon}</span>
                  {category.name}
                </button>
              ))}
            </div>

            {/* Mobile Filter (< 900px) */}
            <div className="min-[900px]:hidden">
              <div className="relative">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                >
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 10l5 5 5-5z"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Announcements Grid - Responsive */}
        <div className="grid grid-cols-1 min-[900px]:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredAnnouncements.map((announcement, index) => (
            <div key={announcement.id}>
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100">
                {/* Card Header */}
                <div className="p-6 pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(announcement.priority)}`}>
                        {getPriorityText(announcement.priority)}
                      </span>
                      {announcement.isNew && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium border border-blue-200">
                          Baru
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{formatDate(announcement.date)}</p>
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                    {announcement.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm leading-relaxed line-clamp-3 mb-4">
                    {announcement.content}
                  </p>
                </div>

                {/* Card Footer */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-500">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      {announcement.author}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      {announcement.views}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredAnnouncements.length === 0 && (
          <div className="text-center py-12">
            <div className="bg-white rounded-xl shadow-lg p-8 max-w-md mx-auto">
              <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Tidak Ada Pengumuman</h3>
              <p className="text-gray-600">
                Belum ada pengumuman untuk kategori yang dipilih.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Students;
